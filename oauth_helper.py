#!/usr/bin/env python3
"""
OAuth Helper Script for Zoho CRM MCP Server

This script helps you complete the OAuth 2.0 flow to get the required tokens
for your Zoho CRM server-based application.

Usage:
1. python oauth_helper.py --get-auth-url
2. Visit the URL, grant permissions, and copy the authorization code
3. python oauth_helper.py --exchange-code YOUR_CODE_HERE
4. Update your .env file with the refresh token

Based on Zoho CRM Python SDK v8.0 documentation from Context7.
"""

import argparse
import requests
import urllib.parse
from src.config import Settings


class ZohoOAuthHelper:
    def __init__(self):
        self.settings = Settings()

        # Determine the accounts domain based on environment
        self.domain_map = {
            "US": "https://accounts.zoho.com",
            "EU": "https://accounts.zoho.eu",
            "IN": "https://accounts.zoho.in",
        }
        self.accounts_domain = self.domain_map.get(
            self.settings.zoho_environment, "https://accounts.zoho.com"
        )

    def get_authorization_url(
        self, redirect_uri: str = "http://localhost:8000/callback", scopes: list = None
    ) -> str:
        """
        Generate authorization URL for OAuth 2.0 flow.
        User needs to visit this URL to grant permissions.
        """
        if scopes is None:
            # Updated scopes based on Context7 documentation for comprehensive access
            scopes = [
                "ZohoCRM.modules.ALL",
                "ZohoCRM.settings.ALL",
                "ZohoCRM.users.ALL",
                "ZohoCRM.org.ALL",
                "ZohoCRM.bulk.ALL",
                "ZohoCRM.notifications.ALL",
            ]

        params = {
            "response_type": "code",
            "client_id": self.settings.zoho_client_id,
            "scope": ",".join(scopes),
            "redirect_uri": redirect_uri,
            "access_type": "offline",
        }

        auth_url = f"{self.accounts_domain}/oauth/v2/auth?" + urllib.parse.urlencode(
            params
        )
        return auth_url

    def exchange_code_for_tokens(
        self,
        authorization_code: str,
        redirect_uri: str = "http://localhost:8000/callback",
    ) -> dict:
        """
        Exchange authorization code for access and refresh tokens.
        Call this after user grants permissions and you receive the code.
        """
        token_url = f"{self.accounts_domain}/oauth/v2/token"

        data = {
            "grant_type": "authorization_code",
            "client_id": self.settings.zoho_client_id,
            "client_secret": self.settings.zoho_client_secret,
            "redirect_uri": redirect_uri,
            "code": authorization_code,
        }

        response = requests.post(token_url, data=data)

        if response.status_code == 200:
            token_data = response.json()
            return token_data
        else:
            raise Exception(
                f"Failed to exchange code for tokens: {response.status_code} - {response.text}"
            )

    def test_tokens(self, refresh_token: str) -> bool:
        """
        Test if the refresh token works by trying to refresh the access token.
        """
        try:
            from zohocrmsdk.src.com.zoho.api.authenticator.oauth_token import OAuthToken
            from zohocrmsdk.src.com.zoho.crm.api.initializer import Initializer
            from zohocrmsdk.src.com.zoho.crm.api.dc import (
                USDataCenter,
                EUDataCenter,
                INDataCenter,
            )

            # Configure environment
            environment_map = {
                "US": USDataCenter.PRODUCTION(),
                "EU": EUDataCenter.PRODUCTION(),
                "IN": INDataCenter.PRODUCTION(),
            }
            environment = environment_map.get(
                self.settings.zoho_environment, USDataCenter.PRODUCTION()
            )

            # Create OAuth token with refresh token
            token = OAuthToken(
                client_id=self.settings.zoho_client_id,
                client_secret=self.settings.zoho_client_secret,
                refresh_token=refresh_token,
            )

            # Try to initialize SDK
            Initializer.initialize(environment, token)
            print("✅ Token test successful! SDK initialized correctly.")
            return True

        except Exception as e:
            print(f"❌ Token test failed: {str(e)}")
            return False

    def diagnose_scope_issues(self, refresh_token: str) -> dict:
        """
        Diagnose potential scope issues by testing API access.
        """
        try:
            from zohocrmsdk.src.com.zoho.api.authenticator.oauth_token import OAuthToken
            from zohocrmsdk.src.com.zoho.crm.api.initializer import Initializer
            from zohocrmsdk.src.com.zoho.crm.api.modules import ModulesOperations
            from zohocrmsdk.src.com.zoho.crm.api.users import UsersOperations
            from zohocrmsdk.src.com.zoho.crm.api.org import OrgOperations

            # Configure environment
            environment_map = {
                "US": "USDataCenter.PRODUCTION()",
                "EU": "EUDataCenter.PRODUCTION()",
                "IN": "INDataCenter.PRODUCTION()",
            }

            from zohocrmsdk.src.com.zoho.crm.api.dc import (
                USDataCenter,
                EUDataCenter,
                INDataCenter,
            )

            environment_instances = {
                "US": USDataCenter.PRODUCTION(),
                "EU": EUDataCenter.PRODUCTION(),
                "IN": INDataCenter.PRODUCTION(),
            }
            environment = environment_instances.get(
                self.settings.zoho_environment, USDataCenter.PRODUCTION()
            )

            # Create OAuth token with refresh token
            token = OAuthToken(
                client_id=self.settings.zoho_client_id,
                client_secret=self.settings.zoho_client_secret,
                refresh_token=refresh_token,
            )

            # Initialize SDK
            Initializer.initialize(environment, token)

            results = {
                "modules_access": False,
                "users_access": False,
                "org_access": False,
                "errors": [],
            }

            # Test modules access
            try:
                modules_operations = ModulesOperations()
                response = modules_operations.get_modules()
                if response and response.get_status_code() == 200:
                    results["modules_access"] = True
                    print("✅ Modules access: OK")
                else:
                    results["errors"].append(
                        f"Modules access failed: {response.get_status_code() if response else 'No response'}"
                    )
                    print("❌ Modules access: FAILED")
            except Exception as e:
                results["errors"].append(f"Modules access error: {str(e)}")
                print(f"❌ Modules access: ERROR - {str(e)}")

            # Test users access
            try:
                users_operations = UsersOperations()
                response = users_operations.get_users()
                if response and response.get_status_code() == 200:
                    results["users_access"] = True
                    print("✅ Users access: OK")
                else:
                    results["errors"].append(
                        f"Users access failed: {response.get_status_code() if response else 'No response'}"
                    )
                    print("❌ Users access: FAILED")
            except Exception as e:
                results["errors"].append(f"Users access error: {str(e)}")
                print(f"❌ Users access: ERROR - {str(e)}")

            # Test org access
            try:
                org_operations = OrgOperations()
                response = org_operations.get_organization()
                if response and response.get_status_code() == 200:
                    results["org_access"] = True
                    print("✅ Organization access: OK")
                else:
                    results["errors"].append(
                        f"Organization access failed: {response.get_status_code() if response else 'No response'}"
                    )
                    print("❌ Organization access: FAILED")
            except Exception as e:
                results["errors"].append(f"Organization access error: {str(e)}")
                print(f"❌ Organization access: ERROR - {str(e)}")

            return results

        except Exception as e:
            return {
                "modules_access": False,
                "users_access": False,
                "org_access": False,
                "errors": [f"SDK initialization failed: {str(e)}"],
            }


def main():
    parser = argparse.ArgumentParser(description="Zoho CRM OAuth Helper")
    parser.add_argument(
        "--get-auth-url", action="store_true", help="Generate authorization URL"
    )
    parser.add_argument(
        "--exchange-code", type=str, help="Exchange authorization code for tokens"
    )
    parser.add_argument(
        "--redirect-uri",
        type=str,
        default="http://localhost:8000/callback",
        help="Redirect URI (default: http://localhost:8000/callback)",
    )
    parser.add_argument("--test-token", type=str, help="Test a refresh token")
    parser.add_argument(
        "--diagnose-scopes", type=str, help="Diagnose scope issues with a refresh token"
    )

    args = parser.parse_args()

    helper = ZohoOAuthHelper()

    if args.get_auth_url:
        print("🔗 Generating authorization URL...")
        print(f"📍 Environment: {helper.settings.zoho_environment}")
        print(f"🌐 Accounts Domain: {helper.accounts_domain}")
        print(f"🔑 Client ID: {helper.settings.zoho_client_id}")
        print()

        auth_url = helper.get_authorization_url(args.redirect_uri)
        print("📋 STEP 1: Visit this URL in your browser:")
        print(f"   {auth_url}")
        print()
        print(
            "📋 STEP 2: Grant permissions and copy the 'code' parameter from the redirect URL"
        )
        print("📋 STEP 3: Run this command with your code:")
        print(
            f"   python oauth_helper.py --exchange-code YOUR_CODE_HERE --redirect-uri {args.redirect_uri}"
        )

    elif args.exchange_code:
        print("🔄 Exchanging authorization code for tokens...")
        try:
            tokens = helper.exchange_code_for_tokens(
                args.exchange_code, args.redirect_uri
            )

            print("✅ Success! Received tokens:")
            print(f"   Access Token: {tokens.get('access_token', 'N/A')[:20]}...")
            print(f"   Refresh Token: {tokens.get('refresh_token', 'N/A')}")
            print(f"   Expires In: {tokens.get('expires_in', 'N/A')} seconds")
            print()

            # Test the refresh token
            refresh_token = tokens.get("refresh_token")
            if refresh_token:
                print("🧪 Testing refresh token...")
                if helper.test_tokens(refresh_token):
                    print()
                    print("📝 UPDATE YOUR .env FILE:")
                    print(f"   ZOHO_REFRESH_TOKEN={refresh_token}")
                    print()
                    print("🎉 You can now start your MCP server!")
                else:
                    print("❌ Token test failed. Please try the OAuth flow again.")

        except Exception as e:
            print(f"❌ Error: {str(e)}")

    elif args.test_token:
        print("🧪 Testing refresh token...")
        if helper.test_tokens(args.test_token):
            print("🎉 Token is valid! You can use it in your .env file.")
        else:
            print("❌ Token is invalid. Please generate a new one.")

    elif args.diagnose_scopes:
        print("🔍 Diagnosing scope issues...")
        print(
            "This will test access to different Zoho CRM APIs to identify scope problems."
        )
        print()

        results = helper.diagnose_scope_issues(args.diagnose_scopes)

        print("\n📊 DIAGNOSIS RESULTS:")
        print("=" * 50)

        if (
            results["modules_access"]
            and results["users_access"]
            and results["org_access"]
        ):
            print("🎉 All API access tests passed! Your token has sufficient scopes.")
        else:
            print("⚠️  Some API access tests failed. This indicates scope issues.")
            print("\n🔧 RECOMMENDED ACTIONS:")

            if not results["modules_access"]:
                print("   • Re-generate OAuth token with ZohoCRM.modules.ALL scope")
            if not results["users_access"]:
                print("   • Re-generate OAuth token with ZohoCRM.users.ALL scope")
            if not results["org_access"]:
                print("   • Re-generate OAuth token with ZohoCRM.org.ALL scope")

            print("\n📝 TO FIX:")
            print("   1. python oauth_helper.py --get-auth-url")
            print("   2. Visit the URL and grant ALL requested permissions")
            print("   3. python oauth_helper.py --exchange-code YOUR_NEW_CODE")
            print("   4. Update your .env file with the new refresh token")

        if results["errors"]:
            print(f"\n❌ ERRORS ENCOUNTERED:")
            for error in results["errors"]:
                print(f"   • {error}")

    else:
        print("❓ No action specified. Use --help for usage information.")
        print()
        print("Quick start:")
        print("1. python oauth_helper.py --get-auth-url")
        print("2. Visit the URL and grant permissions")
        print("3. python oauth_helper.py --exchange-code YOUR_CODE")


if __name__ == "__main__":
    main()
