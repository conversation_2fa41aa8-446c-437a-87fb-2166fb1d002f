"""
Configuration management for the Zoho CRM MCP Server.
Uses Pydantic settings for environment variable management.
"""

from pydantic_settings import BaseSettings
from typing import Optional, List


class Settings(BaseSettings):
    """Configuration settings for the Zoho CRM MCP Server"""

    # Zoho CRM Configuration
    zoho_client_id: str
    zoho_client_secret: str
    zoho_refresh_token: Optional[str] = None
    zoho_environment: str = "US"  # US, EU, IN, CN, AU

    # Authentication Configuration
    auth_enabled: bool = True
    auth_cache_ttl: int = 300  # Token validation cache TTL in seconds
    auth_required_scopes: List[str] = [
        "ZohoCRM.modules.ALL",
        "ZohoCRM.settings.ALL",
        "ZohoCRM.users.ALL",
        "ZohoCRM.org.ALL",
    ]
    auth_exclude_paths: List[str] = [
        "/auth/authorize",
        "/auth/callback",
        "/auth/requirements",
        "/health",
        "/docs",
        "/openapi.json",
    ]

    # OAuth Configuration
    oauth_redirect_uri: str = "http://localhost:8000/auth/callback"

    # Server Configuration
    server_name: str = "Zoho CRM MCP Server"
    log_level: str = "INFO"

    # Rate Limiting
    rate_limit_requests: int = 100
    rate_limit_window: int = 60  # seconds

    # Development Settings
    debug: bool = False

    # HTTP Server Configuration
    server_host: str = "0.0.0.0"
    server_port: int = 8000
    server_path: str = "/mcp"

    class Config:
        env_file = ".env"
        case_sensitive = False

    def get_data_center(self):
        """Get the appropriate Zoho data center based on environment"""
        from zohocrmsdk.src.com.zoho.crm.api.dc import (
            USDataCenter,
            EUDataCenter,
            INDataCenter,
        )

        data_centers = {
            "US": USDataCenter.PRODUCTION(),
            "EU": EUDataCenter.PRODUCTION(),
            "IN": INDataCenter.PRODUCTION(),
        }

        return data_centers.get(self.zoho_environment, USDataCenter.PRODUCTION())
