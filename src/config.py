#!/usr/bin/env python3
"""
Configuration module for Zoho CRM MCP Server
Handles all environment variables and settings for production deployment.
"""

import os
from typing import Dict, Optional
from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings


class ServerConfig(BaseModel):
    """Server configuration settings"""
    host: str = Field(default="127.0.0.1", description="Server host address")
    port: int = Field(default=8000, description="Server port", ge=1, le=65535)
    name: str = Field(default="Zoho CRM MCP Server", description="Server name")
    version: str = Field(default="1.0.0", description="Server version")
    debug: bool = Field(default=False, description="Enable debug mode")
    log_level: str = Field(default="INFO", description="Logging level")
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'log_level must be one of {valid_levels}')
        return v.upper()


class ZohoConfig(BaseModel):
    """Zoho CRM API configuration"""
    environment: str = Field(default="US", description="Zoho environment")
    client_id: str = Field(default="", description="Zoho OAuth client ID")
    client_secret: str = Field(default="", description="Zoho OAuth client secret")
    base_url: Optional[str] = Field(default=None, description="Custom base URL override")
    api_version: str = Field(default="v6", description="Zoho API version")
    timeout: int = Field(default=30, description="API request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum API retry attempts")
    
    @validator('environment')
    def validate_environment(cls, v):
        valid_envs = ['US', 'EU', 'IN', 'AU', 'CN', 'JP']
        if v.upper() not in valid_envs:
            raise ValueError(f'environment must be one of {valid_envs}')
        return v.upper()
    
    @property
    def api_base_url(self) -> str:
        """Get the API base URL based on environment"""
        if self.base_url:
            return self.base_url
        
        env_urls = {
            'US': 'https://www.zohoapis.com',
            'EU': 'https://www.zohoapis.eu', 
            'IN': 'https://www.zohoapis.in',
            'AU': 'https://www.zohoapis.com.au',
            'CN': 'https://www.zohoapis.com.cn',
            'JP': 'https://www.zohoapis.jp'
        }
        return f"{env_urls.get(self.environment, env_urls['US'])}/crm/{self.api_version}"


class CORSConfig(BaseModel):
    """CORS configuration for the server"""
    allow_origins: list = Field(default=["*"], description="Allowed origins")
    allow_methods: list = Field(default=["*"], description="Allowed HTTP methods")
    allow_headers: list = Field(default=["*"], description="Allowed headers")
    allow_credentials: bool = Field(default=True, description="Allow credentials")
    max_age: int = Field(default=600, description="Preflight cache max age")


class SecurityConfig(BaseModel):
    """Security configuration"""
    require_auth: bool = Field(default=True, description="Require authentication")
    auth_header_name: str = Field(default="Authorization", description="Auth header name")
    auth_scheme: str = Field(default="Bearer", description="Auth scheme")
    rate_limit_enabled: bool = Field(default=False, description="Enable rate limiting")
    rate_limit_requests: int = Field(default=100, description="Requests per minute")


class Settings(BaseSettings):
    """Main application settings"""
    
    # Server configuration
    server: ServerConfig = Field(default_factory=ServerConfig)
    
    # Zoho configuration  
    zoho: ZohoConfig = Field(default_factory=ZohoConfig)
    
    # CORS configuration
    cors: CORSConfig = Field(default_factory=CORSConfig)
    
    # Security configuration
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        env_nested_delimiter = "__"
        case_sensitive = False
        extra = "ignore"
    
    @classmethod
    def from_env(cls) -> 'Settings':
        """Create settings from environment variables"""
        return cls(
            server=ServerConfig(
                host=os.getenv('SERVER_HOST', '127.0.0.1'),
                port=int(os.getenv('SERVER_PORT', '8000')),
                name=os.getenv('SERVER_NAME', 'Zoho CRM MCP Server'),
                version=os.getenv('SERVER_VERSION', '1.0.0'),
                debug=os.getenv('SERVER_DEBUG', 'false').lower() == 'true',
                log_level=os.getenv('SERVER_LOG_LEVEL', 'INFO'),
            ),
            zoho=ZohoConfig(
                environment=os.getenv('ZOHO_ENVIRONMENT', 'US'),
                client_id=os.getenv('ZOHO_CLIENT_ID', ''),
                client_secret=os.getenv('ZOHO_CLIENT_SECRET', ''),
                base_url=os.getenv('ZOHO_BASE_URL'),
                api_version=os.getenv('ZOHO_API_VERSION', 'v6'),
                timeout=int(os.getenv('ZOHO_TIMEOUT', '30')),
                max_retries=int(os.getenv('ZOHO_MAX_RETRIES', '3')),
            ),
            cors=CORSConfig(
                allow_origins=os.getenv('CORS_ALLOW_ORIGINS', '*').split(','),
                allow_methods=os.getenv('CORS_ALLOW_METHODS', '*').split(','),
                allow_headers=os.getenv('CORS_ALLOW_HEADERS', '*').split(','),
                allow_credentials=os.getenv('CORS_ALLOW_CREDENTIALS', 'true').lower() == 'true',
                max_age=int(os.getenv('CORS_MAX_AGE', '600')),
            ),
            security=SecurityConfig(
                require_auth=os.getenv('SECURITY_REQUIRE_AUTH', 'true').lower() == 'true',
                auth_header_name=os.getenv('SECURITY_AUTH_HEADER', 'Authorization'),
                auth_scheme=os.getenv('SECURITY_AUTH_SCHEME', 'Bearer'),
                rate_limit_enabled=os.getenv('SECURITY_RATE_LIMIT_ENABLED', 'false').lower() == 'true',
                rate_limit_requests=int(os.getenv('SECURITY_RATE_LIMIT_REQUESTS', '100')),
            )
        )
    
    def get_server_url(self) -> str:
        """Get the full server URL"""
        return f"http://{self.server.host}:{self.server.port}"
    
    def validate_required_settings(self) -> Dict[str, str]:
        """Validate required settings and return any missing ones"""
        missing = {}
        
        if self.security.require_auth and not self.zoho.client_id:
            missing['ZOHO_CLIENT_ID'] = 'Required when authentication is enabled'
            
        if self.security.require_auth and not self.zoho.client_secret:
            missing['ZOHO_CLIENT_SECRET'] = 'Required when authentication is enabled'
            
        return missing


# Global settings instance
settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get the global settings instance"""
    global settings
    if settings is None:
        settings = Settings.from_env()
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment"""
    global settings
    settings = Settings.from_env()
    return settings
