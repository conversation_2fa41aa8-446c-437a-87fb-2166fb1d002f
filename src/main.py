#!/usr/bin/env python3
"""
DEPRECATED: This is the old complex SDK-based implementation.
Use src/simple_main.py for the new clean REST API version.

To use the simplified version:
python src/simple_main.py

Or update this file to redirect to the simple version.
"""

print("⚠️  WARNING: You are running the old complex SDK-based version!")
print("🚀 Use the new simplified version instead:")
print("   python src/simple_main.py")
print("")
print("📖 See MIGRATION_GUIDE.md for migration instructions")
print("📋 See README_SIMPLE.md for usage instructions")
print("")

# Redirect to simple version
import sys
import subprocess

if __name__ == "__main__":
    print("🔄 Automatically redirecting to simplified version...")
    try:
        subprocess.run([sys.executable, "src/simple_main.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error running simplified version: {e}")
        print("💡 Try running directly: python src/simple_main.py")

# Keep the old code below for reference, but don't execute it
if False:  # Disabled old code
    from mcp.server.fastmcp import FastMCP
    from fastmcp.server.dependencies import get_http_headers
    from zoho_service import ZohoCRMService
    from config import Settings
    import asyncio
    import logging

    # Initialize configuration
    settings = Settings()

    # Initialize MCP Server
    mcp = FastMCP("Zoho CRM MCP Server")

# Add authentication middleware if enabled
if settings.auth_enabled:
    from auth.middleware import ZohoAuthMiddleware
    from starlette.middleware import Middleware

    # Create Starlette middleware wrapper for FastMCP
    auth_middleware = Middleware(
        ZohoAuthMiddleware,
        client_id=settings.zoho_client_id,
        client_secret=settings.zoho_client_secret,
        environment=settings.zoho_environment,
        required_scopes=[
            "ZohoCRM.modules.ALL",
            "ZohoCRM.settings.ALL",
            "ZohoCRM.users.ALL",
            "ZohoCRM.org.ALL",
        ],
        cache_ttl=300,  # 5 minutes
        exclude_paths=[
            "/auth/authorize",
            "/auth/callback",
            "/auth/requirements",
            "/health",
            "/docs",
            "/openapi.json",
        ],
    )

    logging.info("Authentication middleware configured for MCP server")

# Initialize Zoho service with dynamic initialization architecture
try:
    # Force dynamic initialization mode by skipping SDK init at startup
    # This implements the user's requested architecture: "initialize the tool at the time of the tool calls"
    zoho_service = ZohoCRMService(settings, skip_sdk_init=True)

    logging.info("Zoho CRM service initialized in dynamic mode")
    logging.info(
        "SDK will be initialized per request using Bearer tokens from Authorization headers"
    )
    logging.info(
        "Usage: Include 'Authorization: Bearer YOUR_ACCESS_TOKEN' in MCP tool requests"
    )

except Exception as e:
    logging.error(f"Failed to initialize Zoho CRM service: {e}")
    logging.info("Service will be initialized dynamically when first tool is called")
    # Continue without Zoho service - will be initialized on first tool call
    zoho_service = None

# Add authentication tools if enabled
if settings.auth_enabled:
    from auth.zoho_provider import ZohoOAuthProvider
    from auth.models import create_zoho_auth_error

    oauth_provider = ZohoOAuthProvider(
        client_id=settings.zoho_client_id,
        client_secret=settings.zoho_client_secret,
        environment=settings.zoho_environment,
        redirect_uri=settings.oauth_redirect_uri,
    )

    @mcp.tool()
    async def get_auth_url(redirect_uri: str = None) -> dict:
        """Get OAuth authorization URL for Zoho CRM authentication"""
        try:
            if not redirect_uri:
                redirect_uri = settings.oauth_redirect_uri

            auth_request = oauth_provider.create_authorization_request()

            return {
                "authorization_url": auth_request["authorization_url"],
                "state": auth_request["state"],
                "code_verifier": auth_request["code_verifier"],
                "redirect_uri": redirect_uri,
                "instructions": [
                    "1. Visit the authorization_url in your browser",
                    "2. Grant permissions to the application",
                    "3. Copy the authorization code from the callback URL",
                    "4. Use the code with exchange_code_for_token tool",
                ],
            }
        except Exception as e:
            return {"error": str(e), "error_type": type(e).__name__}

    @mcp.tool()
    async def exchange_code_for_token(
        authorization_code: str, code_verifier: str, redirect_uri: str = None
    ) -> dict:
        """Exchange authorization code for access token"""
        try:
            if not redirect_uri:
                redirect_uri = settings.oauth_redirect_uri

            token_response = await oauth_provider.exchange_code_for_token(
                authorization_code=authorization_code,
                redirect_uri=redirect_uri,
                code_verifier=code_verifier,
            )

            # Debug: Return the full response to see the structure
            return {
                "success": True,
                "token_response": token_response,
                "access_token": token_response.get("access_token"),
                "token_type": token_response.get("token_type", "Bearer"),
                "expires_in": token_response.get("expires_in"),
                "scope": token_response.get("scope"),
                "instructions": [
                    "Use this access_token in the Authorization header:",
                    f"Authorization: Bearer {token_response.get('access_token', 'TOKEN_NOT_FOUND')}",
                ],
            }
        except Exception as e:
            return {"error": str(e), "error_type": type(e).__name__}

    @mcp.tool()
    async def get_auth_requirements() -> dict:
        """Get authentication requirements and configuration"""
        return create_zoho_auth_error(
            authorization_url="/auth/authorize", environment=settings.zoho_environment
        ).to_dict()

    logging.info("Authentication tools added to MCP server")


# Add health check tool
@mcp.tool()
async def health_check() -> dict:
    """Health check for the Zoho CRM MCP server"""
    return {
        "status": "healthy",
        "service": "zoho-crm-mcp",
        "auth_enabled": settings.auth_enabled,
        "zoho_environment": settings.zoho_environment,
    }


@mcp.tool()
async def debug_headers() -> dict:
    """Debug tool to check what headers are being received and Bearer token extraction"""
    try:
        # Get all headers
        headers = get_http_headers()

        # Extract Bearer token
        access_token = extract_bearer_token()

        # Get authorization header specifically
        auth_header = headers.get("authorization") or headers.get("Authorization")

        return {
            "status": "success",
            "headers_received": dict(headers),
            "authorization_header": auth_header,
            "bearer_token_extracted": access_token is not None,
            "bearer_token_length": len(access_token) if access_token else 0,
            "bearer_token_preview": (
                access_token[:10] + "..."
                if access_token and len(access_token) > 10
                else access_token
            ),
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__,
        }


# HELPER FUNCTIONS
def ensure_zoho_service():
    """Ensure zoho_service is initialized, initialize if None"""
    global zoho_service
    if not zoho_service:
        try:
            zoho_service = ZohoCRMService(settings, skip_sdk_init=True)
            logging.info("Zoho service initialized dynamically")
        except Exception as e:
            raise Exception(f"Failed to initialize Zoho service: {str(e)}")
    return zoho_service


def extract_bearer_token():
    """Extract Bearer token from Authorization header using FastMCP dependencies"""
    try:
        # Use FastMCP's proper dependency function to get headers
        headers = get_http_headers()
        auth_header = headers.get("authorization") or headers.get("Authorization")

        if auth_header and auth_header.startswith("Bearer "):
            return auth_header[7:]  # Remove "Bearer " prefix
        elif auth_header:
            logging.debug(
                f"Authorization header found but not Bearer format: {auth_header[:20]}..."
            )
        else:
            logging.debug("No Authorization header found in request")

    except Exception as e:
        logging.debug(f"Could not extract access token from request: {e}")
    return None


# TOOLS IMPLEMENTATION
@mcp.tool()
async def create_record(
    module: str,
    record_data: dict,
    duplicate_check: bool = True,
    trigger_workflows: bool = False,
    approval_process: bool = False,
) -> dict:
    """
    Create a new record in the specified Zoho CRM module.

    Args:
        module: Zoho CRM module name (e.g., 'Leads', 'Contacts', 'Accounts')
        record_data: Dictionary containing field names and values
        duplicate_check: Whether to check for duplicates before creating
        trigger_workflows: Whether to trigger associated workflows
        approval_process: Whether to trigger approval processes

    Returns:
        Dictionary with creation result including record ID and status

    Example:
        create_record(
            module="Leads",
            record_data={
                "Last_Name": "Smith",
                "Company": "ABC Corp",
                "Email": "<EMAIL>",
                "Lead_Source": "Website"
            }
        )
    """
    try:
        service = ensure_zoho_service()
        access_token = extract_bearer_token()

        result = await service.create_record(
            module=module,
            record_data=record_data,
            duplicate_check=duplicate_check,
            trigger_workflows=trigger_workflows,
            approval_process=approval_process,
            access_token=access_token,
        )

        # Check if the result indicates an error (e.g., SDK not initialized)
        if not result.get("success", True):
            return {
                "success": False,
                "record_id": None,
                "message": result.get("message", "Failed to create record"),
                "error": result.get("error"),
                "error_type": result.get("error_type"),
                "instructions": result.get("instructions", []),
            }

        return {
            "success": True,
            "record_id": result.get("id"),
            "message": result.get("message", "Record created successfully"),
            "details": result.get("details", {}),
        }
    except Exception as e:
        if "Failed to initialize Zoho service" in str(e):
            return {
                "success": False,
                "error": "SERVICE_INITIALIZATION_FAILED",
                "error_type": "InitializationError",
                "message": str(e),
            }
        logging.error(f"Error creating record in {module}: {str(e)}")
        return {"success": False, "error": str(e), "error_type": type(e).__name__}


@mcp.tool()
async def fetch_records(
    module: str,
    fields: list = None,
    criteria: dict = None,
    sort_by: str = None,
    sort_order: str = "asc",
    page: int = 1,
    per_page: int = 50,
    max_records: int = None,
) -> dict:
    """
    Fetch records from the specified Zoho CRM module with advanced filtering.

    Args:
        module: Zoho CRM module name
        fields: List of field names to retrieve (None for all fields)
        criteria: Filter criteria dictionary
        sort_by: Field name to sort by
        sort_order: Sort order ('asc' or 'desc')
        page: Page number (1-based)
        per_page: Records per page (max 200)
        max_records: Maximum total records to return

    Returns:
        Dictionary with records array and metadata

    Example:
        fetch_records(
            module="Leads",
            fields=["Last_Name", "Company", "Email", "Lead_Status"],
            criteria={"Lead_Status": "Open - Not Contacted"},
            sort_by="Created_Time",
            sort_order="desc"
        )
    """
    try:
        service = ensure_zoho_service()
        access_token = extract_bearer_token()

        result = await service.fetch_records(
            module=module,
            fields=fields,
            criteria=criteria,
            sort_by=sort_by,
            sort_order=sort_order,
            page=page,
            per_page=per_page,
            max_records=max_records,
            access_token=access_token,
        )

        # Check if the result indicates an error (e.g., SDK not initialized)
        if not result.get("success", True):
            return {
                "success": False,
                "records": [],
                "count": 0,
                "total_count": 0,
                "page": page,
                "per_page": per_page,
                "has_more": False,
                "error": result.get("error"),
                "error_type": result.get("error_type"),
                "message": result.get("message", "Failed to fetch records"),
            }

        return {
            "success": True,
            "records": result.get("records", []),
            "count": len(result.get("records", [])),
            "total_count": result.get("total_count"),
            "page": page,
            "per_page": per_page,
            "has_more": result.get("has_more", False),
        }
    except Exception as e:
        logging.error(f"Error fetching records from {module}: {str(e)}")
        return {"success": False, "error": str(e), "error_type": type(e).__name__}


@mcp.tool()
async def search_records(
    modules: list,
    search_text: str,
    fields: list = None,
    criteria: dict = None,
    max_results: int = 100,
) -> dict:
    """
    Search across multiple Zoho CRM modules using text search.

    Args:
        modules: List of module names to search in
        search_text: Text to search for
        fields: Specific fields to search in (None for all searchable fields)
        criteria: Additional filter criteria
        max_results: Maximum number of results to return

    Returns:
        Dictionary with search results grouped by module
    """
    try:
        service = ensure_zoho_service()
        access_token = extract_bearer_token()

        results = await service.search_records(
            modules=modules,
            search_text=search_text,
            fields=fields,
            criteria=criteria,
            max_results=max_results,
            access_token=access_token,
        )

        # Check if the result indicates an error (e.g., SDK not initialized)
        if not results.get("success", True):
            return {
                "success": False,
                "results": {module: [] for module in modules},
                "total_found": 0,
                "searched_modules": modules,
                "error": results.get("error"),
                "error_type": results.get("error_type"),
                "message": results.get("message", "Failed to search records"),
            }

        return {
            "success": True,
            "results": results,
            "total_found": sum(
                len(module_results) for module_results in results.values()
            ),
            "searched_modules": modules,
        }
    except Exception as e:
        if "Failed to initialize Zoho service" in str(e):
            return {
                "success": False,
                "error": "SERVICE_INITIALIZATION_FAILED",
                "error_type": "InitializationError",
                "message": str(e),
                "results": {module: [] for module in modules},
            }
        logging.error(f"Error searching records: {str(e)}")
        return {"success": False, "error": str(e), "error_type": type(e).__name__}


# RESOURCES IMPLEMENTATION
@mcp.resource("zoho://modules/{module}/schema")
async def get_module_schema(module: str) -> dict:
    """
    Get complete schema information for a Zoho CRM module.

    Returns field definitions, relationships, permissions, and module metadata.
    """
    try:
        service = ensure_zoho_service()
        access_token = extract_bearer_token()

        schema = await service.get_module_schema(module, access_token=access_token)
        return {
            "module": module,
            "schema": schema,
            "last_updated": schema.get("modified_time"),
        }
    except Exception as e:
        if "Failed to initialize Zoho service" in str(e):
            return {
                "error": "SERVICE_INITIALIZATION_FAILED",
                "error_type": "InitializationError",
                "message": str(e),
                "module": module,
            }
        return {"error": str(e), "module": module}


@mcp.resource("zoho://modules/{module}/fields")
async def get_module_fields(module: str) -> dict:
    """
    Get detailed field metadata for a Zoho CRM module.

    Includes field types, validation rules, picklist values, and permissions.
    """
    try:
        service = ensure_zoho_service()
        access_token = extract_bearer_token()

        fields = await service.get_module_fields(module, access_token=access_token)
        return {"module": module, "fields": fields, "field_count": len(fields)}
    except Exception as e:
        if "Failed to initialize Zoho service" in str(e):
            return {
                "error": "SERVICE_INITIALIZATION_FAILED",
                "error_type": "InitializationError",
                "message": str(e),
                "module": module,
            }
        return {"error": str(e), "module": module}


@mcp.resource("zoho://modules/{module}/picklists/{field}")
async def get_picklist_values(module: str, field: str) -> dict:
    """
    Get picklist values for a specific field in a module.
    """
    try:
        service = ensure_zoho_service()
        access_token = extract_bearer_token()

        values = await service.get_picklist_values(
            module, field, access_token=access_token
        )
        return {"module": module, "field": field, "values": values}
    except Exception as e:
        if "Failed to initialize Zoho service" in str(e):
            return {
                "error": "SERVICE_INITIALIZATION_FAILED",
                "error_type": "InitializationError",
                "message": str(e),
                "module": module,
                "field": field,
            }
        return {"error": str(e), "module": module, "field": field}


if __name__ == "__main__":
    # Run the MCP server with streamable-http transport for testing with Postman
    if settings.auth_enabled:
        logging.info("🚀 Starting Zoho CRM MCP Server with Bearer Token Authentication")
        logging.info("📋 Usage Instructions:")
        logging.info("   • Include Authorization header in all MCP tool requests")
        logging.info("   • Format: Authorization: Bearer YOUR_ACCESS_TOKEN")
        logging.info("   • The SDK will initialize dynamically for each request")
        logging.info("   • Get access tokens using the exchange_code_for_token tool")
        mcp.run(transport="streamable-http")
    else:
        # Run without authentication
        logging.info("🚀 Starting Zoho CRM MCP Server without authentication")
        mcp.run(transport="streamable-http")
