#!/usr/bin/env python3
"""
Zoho CRM MCP Server
A streamable HTTP Model Context Protocol server for Zoho CRM REST API integration.
Clean, lightweight implementation using standard MCP library.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from contextvars import ContextVar

import httpx
from mcp.server.streamable_http_manager import StreamableHTTPSessionManager
from starlette.middleware.cors import CORSMiddleware
from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.routing import Route
from mcp.server import Server
import mcp.types as types
import contextlib
import uvicorn
from pydantic import BaseModel
from pydantic_settings import BaseSettings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Context variable to store access token
access_token_context: ContextVar[str] = ContextVar("access_token")


class ZohoSettings(BaseSettings):
    """Configuration settings for Zoho CRM API"""

    zoho_client_id: str = ""
    zoho_client_secret: str = ""
    zoho_environment: str = "US"  # US, EU, IN, AU, CN

    # Optional server configuration
    server_name: str = "Zoho CRM MCP Server"
    log_level: str = "INFO"
    debug: bool = False

    class Config:
        env_file = ".env"
        extra = "ignore"  # Ignore extra fields in .env


class ZohoAPI:
    """Zoho CRM REST API client"""

    def __init__(self, settings: ZohoSettings):
        self.settings = settings
        self.base_urls = {
            "US": "https://www.zohoapis.com/crm/v6",
            "EU": "https://www.zohoapis.eu/crm/v6",
            "IN": "https://www.zohoapis.in/crm/v6",
            "AU": "https://www.zohoapis.com.au/crm/v6",
            "CN": "https://www.zohoapis.com.cn/crm/v6",
        }
        self.base_url = self.base_urls.get(
            settings.zoho_environment, self.base_urls["US"]
        )

    def _get_headers(self, access_token: str) -> Dict[str, str]:
        """Get headers for API requests"""
        return {
            "Authorization": f"Zoho-oauthtoken {access_token}",
            "Content-Type": "application/json",
        }

    async def create_record(self, module: str, data: Dict, access_token: str) -> Dict:
        """Create a record using REST API"""
        url = f"{self.base_url}/{module}"
        headers = self._get_headers(access_token)
        payload = {"data": [data]}

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(url, json=payload, headers=headers)
                response.raise_for_status()
                result = response.json()

                if result.get("data") and len(result["data"]) > 0:
                    record = result["data"][0]
                    if record.get("code") == "SUCCESS":
                        return {
                            "success": True,
                            "id": record.get("details", {}).get("id"),
                            "message": record.get(
                                "message", "Record created successfully"
                            ),
                        }
                    else:
                        return {
                            "success": False,
                            "error": record.get("code", "UNKNOWN_ERROR"),
                            "message": record.get("message", "Failed to create record"),
                        }

                return {
                    "success": False,
                    "error": "INVALID_RESPONSE",
                    "message": "No data in response",
                }

            except httpx.HTTPStatusError as e:
                error_detail = ""
                try:
                    error_data = e.response.json()
                    error_detail = error_data.get("message", str(e))
                except Exception:
                    error_detail = str(e)

                return {
                    "success": False,
                    "error": f"HTTP_{e.response.status_code}",
                    "message": f"API request failed: {error_detail}",
                }
            except Exception as e:
                return {"success": False, "error": "REQUEST_FAILED", "message": str(e)}

    async def get_records(
        self,
        module: str,
        access_token: str,
        fields: Optional[List[str]] = None,
        page: int = 1,
        per_page: int = 50,
    ) -> Dict:
        """Get records using REST API"""
        url = f"{self.base_url}/{module}"
        headers = self._get_headers(access_token)

        params = {"page": page, "per_page": min(per_page, 200)}  # Zoho max is 200

        if fields:
            params["fields"] = ",".join(fields)

        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, headers=headers, params=params)
                response.raise_for_status()
                result = response.json()

                records = result.get("data", [])
                return {
                    "success": True,
                    "records": records,
                    "count": len(records),
                    "page": page,
                    "per_page": per_page,
                }

            except httpx.HTTPStatusError as e:
                error_detail = ""
                try:
                    error_data = e.response.json()
                    error_detail = error_data.get("message", str(e))
                except Exception:
                    error_detail = str(e)

                return {
                    "success": False,
                    "error": f"HTTP_{e.response.status_code}",
                    "message": f"API request failed: {error_detail}",
                    "records": [],
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": "REQUEST_FAILED",
                    "message": str(e),
                    "records": [],
                }

    async def search_records(
        self, module: str, criteria: str, access_token: str
    ) -> Dict:
        """Search records using REST API"""
        url = f"{self.base_url}/{module}/search"
        headers = self._get_headers(access_token)
        params = {"criteria": criteria}

        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, headers=headers, params=params)
                response.raise_for_status()
                result = response.json()

                records = result.get("data", [])
                return {"success": True, "records": records, "count": len(records)}

            except httpx.HTTPStatusError as e:
                if e.response.status_code == 204:
                    return {"success": True, "records": [], "count": 0}

                error_detail = ""
                try:
                    error_data = e.response.json()
                    error_detail = error_data.get("message", str(e))
                except Exception:
                    error_detail = str(e)

                return {
                    "success": False,
                    "error": f"HTTP_{e.response.status_code}",
                    "message": f"Search failed: {error_detail}",
                    "records": [],
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": "SEARCH_FAILED",
                    "message": str(e),
                    "records": [],
                }


# Initialize settings and API client
settings = ZohoSettings()
zoho_api = ZohoAPI(settings)

# Initialize MCP Server
server = Server("zoho-crm-mcp")


class HandleStreamableHttp:
    """Custom ASGI endpoint for handling streamable HTTP requests and extracting headers"""

    def __init__(self, session_manager):
        self.session_manager = session_manager

    def _extract_headers(self, scope):
        """Extract authorization headers from request scope."""
        headers = dict(scope.get("headers", []))

        # Extract Authorization header (Bearer token)
        access_token = None
        auth_header = headers.get(b"authorization")
        if auth_header:
            auth_str = auth_header.decode("utf-8")
            if auth_str.startswith("Bearer "):
                access_token = auth_str[7:].strip()
                logger.info("Bearer token extracted from Authorization header")

        return access_token

    async def __call__(self, scope, receive, send):
        if self.session_manager is not None:
            try:
                logger.info("Handling Streamable HTTP connection...")

                # Extract headers
                access_token = self._extract_headers(scope)

                # Set context variable for access token
                if access_token:
                    access_token_context.set(access_token)

                await self.session_manager.handle_request(scope, receive, send)
                logger.info("Streamable HTTP connection closed...")
            except Exception as e:
                logger.error(f"Error handling streamable HTTP request: {e}")
                # Send error response
                response = {
                    "status": 500,
                    "headers": [(b"content-type", b"application/json")],
                }
                await send(response)
                await send(
                    {
                        "type": "http.response.body",
                        "body": json.dumps({"error": str(e)}).encode(),
                    }
                )
        else:
            # No session manager available
            response = {
                "status": 503,
                "headers": [(b"content-type", b"application/json")],
            }
            await send(response)
            await send(
                {
                    "type": "http.response.body",
                    "body": json.dumps(
                        {"error": "Session manager not available"}
                    ).encode(),
                }
            )


def create_authentication_error() -> dict:
    """Create standardized authentication error response"""
    return {
        "error": {
            "code": "AUTHENTICATION_REQUIRED",
            "message": "Authentication required to access this resource",
            "details": {
                "authentication_requirements": [
                    {
                        "provider": "zoho",
                        "auth_type": "bearer",
                        "header_name": "Authorization",
                        "header_format": "Bearer {access_token}",
                        "required_scopes": ["ZohoCRM.modules.ALL"],
                        "token_source": "access_token",
                    }
                ],
            },
        }
    }


def get_access_token() -> Optional[str]:
    """Get access token from context"""
    try:
        return access_token_context.get()
    except LookupError:
        logger.warning("No access token found in context")
        return None


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> list[types.TextContent]:
    """Handle tool calls"""
    access_token = get_access_token()

    if name == "create_record":
        if not access_token:
            error_result = create_authentication_error()
            return [
                types.TextContent(type="text", text=json.dumps(error_result, indent=2))
            ]

        module = arguments.get("module")
        record_data = arguments.get("record_data")

        if not module or not record_data:
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "error": "Missing required parameters: module and record_data"
                        },
                        indent=2,
                    ),
                )
            ]

        result = await zoho_api.create_record(module, record_data, access_token)
        return [types.TextContent(type="text", text=json.dumps(result, indent=2))]

    elif name == "get_records":
        if not access_token:
            error_result = create_authentication_error()
            return [
                types.TextContent(type="text", text=json.dumps(error_result, indent=2))
            ]

        module = arguments.get("module")
        fields = arguments.get("fields")
        page = arguments.get("page", 1)
        per_page = arguments.get("per_page", 50)

        if not module:
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {"error": "Missing required parameter: module"}, indent=2
                    ),
                )
            ]

        result = await zoho_api.get_records(
            module, access_token, fields, page, per_page
        )
        return [types.TextContent(type="text", text=json.dumps(result, indent=2))]

    elif name == "search_records":
        if not access_token:
            error_result = create_authentication_error()
            return [
                types.TextContent(type="text", text=json.dumps(error_result, indent=2))
            ]

        module = arguments.get("module")
        search_criteria = arguments.get("search_criteria")

        if not module or not search_criteria:
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "error": "Missing required parameters: module and search_criteria"
                        },
                        indent=2,
                    ),
                )
            ]

        result = await zoho_api.search_records(module, search_criteria, access_token)
        return [types.TextContent(type="text", text=json.dumps(result, indent=2))]

    elif name == "health_check":
        result = {
            "status": "healthy",
            "service": "zoho-crm-mcp",
            "environment": settings.zoho_environment,
            "base_url": zoho_api.base_url,
        }
        return [types.TextContent(type="text", text=json.dumps(result, indent=2))]

    else:
        return [
            types.TextContent(
                type="text",
                text=json.dumps({"error": f"Unknown tool: {name}"}, indent=2),
            )
        ]


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """List available tools"""
    return [
        types.Tool(
            name="create_record",
            description="Create a new record in Zoho CRM",
            inputSchema={
                "type": "object",
                "properties": {
                    "module": {
                        "type": "string",
                        "description": "CRM module name (e.g., 'Leads', 'Contacts', 'Accounts')",
                    },
                    "record_data": {
                        "type": "object",
                        "description": "Dictionary with field names and values",
                    },
                },
                "required": ["module", "record_data"],
            },
        ),
        types.Tool(
            name="get_records",
            description="Get records from Zoho CRM",
            inputSchema={
                "type": "object",
                "properties": {
                    "module": {"type": "string", "description": "CRM module name"},
                    "fields": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of fields to retrieve (optional)",
                    },
                    "page": {
                        "type": "integer",
                        "description": "Page number (default: 1)",
                        "default": 1,
                    },
                    "per_page": {
                        "type": "integer",
                        "description": "Records per page (default: 50, max: 200)",
                        "default": 50,
                    },
                },
                "required": ["module"],
            },
        ),
        types.Tool(
            name="search_records",
            description="Search records in Zoho CRM",
            inputSchema={
                "type": "object",
                "properties": {
                    "module": {"type": "string", "description": "CRM module name"},
                    "search_criteria": {
                        "type": "string",
                        "description": "Search criteria (e.g., '(Email:equals:<EMAIL>)')",
                    },
                },
                "required": ["module", "search_criteria"],
            },
        ),
        types.Tool(
            name="health_check",
            description="Health check for the MCP server",
            inputSchema={"type": "object", "properties": {}},
        ),
    ]


async def create_app():
    """Create the ASGI application"""
    # Create session manager
    session_manager = None
    try:
        session_manager = StreamableHTTPSessionManager(server)
        logger.info("StreamableHTTPSessionManager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize StreamableHTTPSessionManager: {e}")
        session_manager = None

    # Create routes
    routes = []

    # Add Streamable HTTP route if available
    if session_manager is not None:
        routes.append(
            Route(
                "/",
                endpoint=HandleStreamableHttp(session_manager),
                methods=["POST", "GET"],
            )
        )

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        )
    ]

    # Define lifespan for session manager
    @contextlib.asynccontextmanager
    async def lifespan(app):
        """Context manager for session manager."""
        if session_manager is not None:
            async with session_manager.run():
                logger.info("Application started with StreamableHTTP session manager!")
                try:
                    yield
                finally:
                    logger.info("Application shutting down...")
        else:
            # No session manager, just yield
            yield

    return Starlette(routes=routes, middleware=middleware, lifespan=lifespan)


async def main():
    """Main function to run the MCP server"""
    logger.info("🚀 Starting Zoho CRM MCP Server")
    logger.info(f"📍 Environment: {settings.zoho_environment}")
    logger.info(f"🌐 API Base URL: {zoho_api.base_url}")
    logger.info("💡 Authentication: Authorization header: 'Bearer <token>'")

    app = await create_app()

    # Start the server
    config = uvicorn.Config(app=app, host="127.0.0.1", port=8000, log_level="info")

    server_instance = uvicorn.Server(config)
    logger.info("🌐 Starting streamable HTTP server on http://127.0.0.1:8000")
    logger.info(
        "🔗 Use this server with MCP clients that support streamable HTTP transport"
    )

    await server_instance.serve()


if __name__ == "__main__":
    asyncio.run(main())
