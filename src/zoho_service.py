# src/zoho_service.py
from zohocrmsdk.src.com.zoho.api.authenticator.oauth_token import OAuthToken
from zohocrmsdk.src.com.zoho.crm.api.initializer import Initializer
from zohocrmsdk.src.com.zoho.crm.api.dc import USDataCenter, EUDataCenter, INDataCenter
from zohocrmsdk.src.com.zoho.crm.api.record import *
from zohocrmsdk.src.com.zoho.crm.api.modules import ModulesOperations
from zohocrmsdk.src.com.zoho.crm.api.fields import FieldsOperations
from zohocrmsdk.src.com.zoho.crm.api.parameter_map import ParameterMap
from zohocrmsdk.src.com.zoho.crm.api.util import Choice
import asyncio
import logging
import requests
import urllib.parse
from typing import Dict, List, Optional, Any
from config import Settings


class ZohoCRMService:
    """
    Service layer for Zoho CRM operations using the official Python SDK.
    Handles authentication, API calls, and response processing.
    """

    def __init__(self, settings: Settings, skip_sdk_init: bool = False):
        self.settings = settings
        self.is_initialized = False
        self.access_token = None
        self.refresh_token = getattr(settings, "zoho_refresh_token", None)
        self.skip_sdk_init = skip_sdk_init
        self._current_access_token = (
            None  # Track current token for dynamic initialization
        )

        # For dynamic initialization architecture, we don't initialize at startup
        # Instead, we initialize at tool call time using the Bearer token
        if (
            not skip_sdk_init
            and self.refresh_token
            and self.refresh_token != "test_refresh_token"
        ):
            # Only initialize with refresh token if explicitly requested (legacy mode)
            logging.info("Legacy mode: Initializing SDK with refresh token at startup")
            self._initialize_sdk()
        else:
            logging.info(
                "Dynamic initialization mode: SDK will be initialized per request using Bearer tokens"
            )
            logging.info(
                "Pass access tokens via Authorization header: 'Bearer YOUR_ACCESS_TOKEN'"
            )

    def get_authorization_url(self, redirect_uri: str, scopes: List[str] = None) -> str:
        """
        Generate authorization URL for OAuth 2.0 flow.
        User needs to visit this URL to grant permissions.
        """
        if scopes is None:
            scopes = ["ZohoCRM.modules.ALL", "ZohoCRM.settings.ALL"]

        # Determine the accounts domain based on environment
        domain_map = {
            "US": "https://accounts.zoho.com",
            "EU": "https://accounts.zoho.eu",
            "IN": "https://accounts.zoho.in",
        }
        accounts_domain = domain_map.get(
            self.settings.zoho_environment, "https://accounts.zoho.com"
        )

        params = {
            "response_type": "code",
            "client_id": self.settings.zoho_client_id,
            "scope": ",".join(scopes),
            "redirect_uri": redirect_uri,
            "access_type": "offline",
        }

        auth_url = f"{accounts_domain}/oauth/v2/auth?" + urllib.parse.urlencode(params)
        return auth_url

    def exchange_code_for_tokens(
        self, authorization_code: str, redirect_uri: str
    ) -> Dict[str, str]:
        """
        Exchange authorization code for access and refresh tokens.
        Call this after user grants permissions and you receive the code.
        """
        # Determine the accounts domain based on environment
        domain_map = {
            "US": "https://accounts.zoho.com",
            "EU": "https://accounts.zoho.eu",
            "IN": "https://accounts.zoho.in",
        }
        accounts_domain = domain_map.get(
            self.settings.zoho_environment, "https://accounts.zoho.com"
        )

        token_url = f"{accounts_domain}/oauth/v2/token"

        data = {
            "grant_type": "authorization_code",
            "client_id": self.settings.zoho_client_id,
            "client_secret": self.settings.zoho_client_secret,
            "redirect_uri": redirect_uri,
            "code": authorization_code,
        }

        response = requests.post(token_url, data=data)

        if response.status_code == 200:
            token_data = response.json()
            self.access_token = token_data.get("access_token")
            self.refresh_token = token_data.get("refresh_token")

            logging.info("Successfully obtained OAuth tokens")
            return token_data
        else:
            error_msg = f"Failed to exchange code for tokens: {response.status_code} - {response.text}"
            logging.error(error_msg)
            raise Exception(error_msg)

    def refresh_access_token(self) -> Dict[str, str]:
        """
        Refresh the access token using the refresh token.
        """
        if not self.refresh_token:
            raise Exception("No refresh token available. Need to re-authorize.")

        # Determine the accounts domain based on environment
        domain_map = {
            "US": "https://accounts.zoho.com",
            "EU": "https://accounts.zoho.eu",
            "IN": "https://accounts.zoho.in",
        }
        accounts_domain = domain_map.get(
            self.settings.zoho_environment, "https://accounts.zoho.com"
        )

        token_url = f"{accounts_domain}/oauth/v2/token"

        data = {
            "grant_type": "refresh_token",
            "client_id": self.settings.zoho_client_id,
            "client_secret": self.settings.zoho_client_secret,
            "refresh_token": self.refresh_token,
        }

        response = requests.post(token_url, data=data)

        if response.status_code == 200:
            token_data = response.json()
            self.access_token = token_data.get("access_token")

            logging.info("Successfully refreshed access token")
            return token_data
        else:
            error_msg = f"Failed to refresh access token: {response.status_code} - {response.text}"
            logging.error(error_msg)
            raise Exception(error_msg)

    def _initialize_sdk_with_access_token(self, access_token: str):
        """
        Initialize or switch SDK context using access token (Context7 access token flow)
        This method follows the dynamic initialization pattern for MCP servers
        """
        try:
            # Import required classes (following Context7 documentation)
            from zohocrmsdk.src.com.zoho.api.authenticator.store import FileStore
            from zohocrmsdk.src.com.zoho.crm.api.sdk_config import SDKConfig

            # Configure environment based on settings (Context7 pattern)
            environment_map = {
                "US": USDataCenter.PRODUCTION(),
                "EU": EUDataCenter.PRODUCTION(),
                "IN": INDataCenter.PRODUCTION(),
            }
            environment = environment_map.get(
                self.settings.zoho_environment, USDataCenter.PRODUCTION()
            )

            # Create OAuth token using access token flow (Context7 documentation pattern)
            # This is perfect for dynamic initialization with Bearer tokens
            token = OAuthToken(access_token=access_token)

            # Create FileStore for token persistence (Context7 best practice)
            store = FileStore(file_path="./zoho_dynamic_tokens.txt")

            # Create SDK config following Context7 recommendations
            sdk_config = SDKConfig(
                auto_refresh_fields=False,  # Disable for dynamic mode to avoid conflicts
                pick_list_validation=False,  # Disable to allow any picklist values
                connect_timeout=None,  # Use default timeout
                read_timeout=None,  # Use default timeout
            )

            # Check if SDK is already initialized and we need to switch user
            if self.is_initialized and self._current_access_token != access_token:
                # Use switch_user for different access tokens (Context7 multi-user pattern)
                logging.debug("Switching SDK context to new access token")
                Initializer.switch_user(
                    environment=environment, token=token, sdk_config=sdk_config
                )
            elif not self.is_initialized:
                # Initial SDK initialization (Context7 initialization pattern)
                logging.debug("Initializing SDK with access token")
                Initializer.initialize(
                    environment=environment,
                    token=token,
                    store=store,
                    sdk_config=sdk_config,
                )
                self.is_initialized = True

            # Track current token for future switch_user calls
            self._current_access_token = access_token

            logging.debug(
                f"SDK context set for {self.settings.zoho_environment} environment with access token"
            )
            return True

        except Exception as e:
            logging.error(f"Failed to initialize SDK with access token: {str(e)}")
            return False

    def _extract_access_token_from_auth_header(self, auth_header: str = None) -> str:
        """Extract access token from Authorization header"""
        if not auth_header:
            return None

        if auth_header.startswith("Bearer "):
            return auth_header[7:]  # Remove "Bearer " prefix

        return None

    def _initialize_sdk(self):
        """Initialize Zoho CRM SDK following Context7 documentation patterns"""
        try:
            # Import required classes (following Context7 documentation)
            from zohocrmsdk.src.com.zoho.api.authenticator.store import FileStore
            from zohocrmsdk.src.com.zoho.crm.api.sdk_config import SDKConfig

            # Configure environment based on settings (Context7 pattern)
            environment_map = {
                "US": USDataCenter.PRODUCTION(),
                "EU": EUDataCenter.PRODUCTION(),
                "IN": INDataCenter.PRODUCTION(),
            }
            environment = environment_map.get(
                self.settings.zoho_environment, USDataCenter.PRODUCTION()
            )

            # Validate refresh token availability
            if not self.refresh_token or self.refresh_token == "test_refresh_token":
                raise Exception(
                    "No valid refresh token found. Please complete OAuth authorization first."
                )

            # Create OAuth token using refresh token flow (Context7 documentation pattern)
            # This is the recommended approach for server applications
            token = OAuthToken(
                client_id=self.settings.zoho_client_id,
                client_secret=self.settings.zoho_client_secret,
                refresh_token=self.refresh_token,
            )

            # Create FileStore for token persistence (Context7 best practice)
            # This allows the SDK to automatically refresh and persist tokens
            store = FileStore(file_path="./zoho_sdk_tokens.txt")

            # Create SDK config following Context7 recommendations
            sdk_config = SDKConfig(
                auto_refresh_fields=True,  # Auto-refresh module fields every hour
                pick_list_validation=False,  # Disable to allow any picklist values
                connect_timeout=None,  # Use default timeout
                read_timeout=None,  # Use default timeout
            )

            # Initialize SDK following Context7 documentation pattern
            # This is the core initialization call that sets up the SDK globally
            Initializer.initialize(
                environment=environment, token=token, store=store, sdk_config=sdk_config
            )

            self.is_initialized = True
            logging.info(
                f"Zoho CRM SDK initialized successfully for {self.settings.zoho_environment} environment"
            )
            logging.info(
                "SDK features: FileStore persistence, auto-refresh fields, flexible picklist validation"
            )

        except Exception as e:
            logging.warning(f"Failed to initialize Zoho CRM SDK: {str(e)}")
            logging.warning(
                "Server will start in demo mode - API calls will return error messages"
            )
            logging.info("To enable full functionality:")
            logging.info("1. Run: python oauth_helper.py --get-auth-url")
            logging.info("2. Visit the URL and grant permissions")
            logging.info("3. Run: python oauth_helper.py --exchange-code YOUR_CODE")
            logging.info("4. Update your .env file with the refresh token")
            logging.info("5. Restart the server")
            self.is_initialized = False

    def _get_picklist_fields_cache(self, module: str) -> Dict[str, List[str]]:
        """Get cached picklist fields for a module to avoid repeated API calls"""
        if not hasattr(self, "_picklist_cache"):
            self._picklist_cache = {}

        if module not in self._picklist_cache:
            # Common picklist fields for standard modules
            common_picklists = {
                "Leads": [
                    "Lead_Source",
                    "Industry",
                    "Lead_Status",
                    "Rating",
                    "Salutation",
                ],
                "Contacts": ["Lead_Source", "Salutation", "Department"],
                "Accounts": ["Industry", "Account_Type", "Rating", "Ownership"],
                "Deals": ["Stage", "Deal_Category_Status", "Type", "Lead_Source"],
                "Tasks": ["Status", "Priority"],
                "Events": ["Event_Status"],
                "Calls": ["Call_Type", "Call_Status", "Call_Result"],
                "Products": ["Product_Category", "Product_Active", "Commission_Rate"],
                "Quotes": ["Quote_Stage"],
                "Sales_Orders": ["Status"],
                "Purchase_Orders": ["Status"],
                "Invoices": ["Status"],
                "Campaigns": ["Type", "Status"],
                "Cases": ["Status", "Priority", "Case_Origin", "Case_Reason"],
                "Solutions": ["Status"],
            }
            self._picklist_cache[module] = common_picklists.get(module, [])

        return self._picklist_cache[module]

    def _process_record_data(self, module: str, record_data: dict) -> dict:
        """Process record data to handle picklist fields and data types properly"""
        processed_data = {}
        picklist_fields = self._get_picklist_fields_cache(module)

        # Define fields that need specific data type conversion
        float_fields = {
            "Leads": ["Annual_Revenue"],
            "Contacts": ["Annual_Revenue"],
            "Accounts": ["Annual_Revenue"],
            "Deals": ["Amount", "Exchange_Rate"],
            "Products": ["Unit_Price", "Commission_Rate"],
            "Quotes": ["Grand_Total", "Sub_Total", "Tax", "Adjustment"],
            "Sales_Orders": ["Grand_Total", "Sub_Total", "Tax", "Adjustment"],
            "Purchase_Orders": ["Grand_Total", "Sub_Total", "Tax", "Adjustment"],
            "Invoices": ["Grand_Total", "Sub_Total", "Tax", "Adjustment"],
        }

        module_float_fields = float_fields.get(module, [])

        for field, value in record_data.items():
            if field in picklist_fields and isinstance(value, str):
                # Wrap picklist values in Choice objects
                processed_data[field] = Choice(value)
            elif field in module_float_fields and value is not None:
                # Convert numeric fields to float for proper type handling
                try:
                    processed_data[field] = float(value)
                except (ValueError, TypeError):
                    # If conversion fails, keep original value
                    processed_data[field] = value
            else:
                processed_data[field] = value

        return processed_data

    async def is_authenticated(self) -> bool:
        """Check if the current authentication is valid"""
        return self.is_initialized

    async def refresh_authentication(self):
        """Refresh authentication if needed"""
        if not self.is_initialized:
            self._initialize_sdk()

    async def create_record(
        self,
        module: str,
        record_data: dict,
        duplicate_check: bool = True,
        trigger_workflows: bool = False,
        approval_process: bool = False,
        access_token: str = None,
    ) -> dict:
        """Create a record using the official Zoho SDK with dynamic initialization"""

        # Dynamic initialization with access token
        if access_token:
            if not self._initialize_sdk_with_access_token(access_token):
                return {
                    "success": False,
                    "error": "SDK_INITIALIZATION_FAILED",
                    "error_type": "AuthenticationError",
                    "message": "Failed to initialize SDK with provided access token",
                }
        elif not self.is_initialized:
            return {
                "success": False,
                "error": "SDK_NOT_INITIALIZED",
                "error_type": "InitializationError",
                "message": "Zoho SDK is not initialized. Please provide access token or complete OAuth authentication first.",
                "instructions": [
                    "1. Provide a valid access token in the request",
                    "2. Or run: python oauth_helper.py --get-auth-url",
                    "3. Visit the URL and grant permissions",
                    "4. Run: python oauth_helper.py --exchange-code YOUR_CODE",
                    "5. Update your .env file with the refresh token",
                    "6. Restart the server",
                ],
            }

        def _create_sync():
            try:
                record_operations = RecordOperations(module)
                request = BodyWrapper()

                # Process record data to handle picklist fields
                processed_data = self._process_record_data(module, record_data)

                # Create record object
                record = Record()
                for field, value in processed_data.items():
                    record.add_key_value(field, value)

                request.set_data([record])

                # Configure options
                if duplicate_check:
                    # Set duplicate check fields (commonly Email for most modules)
                    duplicate_fields = ["Email"] if "Email" in record_data else []
                    if duplicate_fields:
                        request.set_duplicate_check_fields(duplicate_fields)

                if trigger_workflows:
                    request.set_trigger(["workflow"])

                if approval_process:
                    request.set_trigger(["approval"])

                # Make API call
                response = record_operations.create_records(request)
                result = self._process_create_response(response)

                # If the response indicates an error, log it but don't raise exception
                # Let the caller handle the error response
                if not result.get("success", True):
                    logging.error(
                        f"Error creating record in {module}: {result.get('message', 'Unknown error')}"
                    )

                return result

            except Exception as e:
                logging.error(f"Error in _create_sync: Caused By: {e} - {str(e)}")
                return {
                    "success": False,
                    "error": "EXCEPTION",
                    "error_type": e.__class__.__name__,
                    "message": str(e),
                }

        # Run in thread pool to avoid blocking the event loop
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _create_sync)

    async def fetch_records(
        self,
        module: str,
        fields: Optional[List[str]] = None,
        criteria: Optional[dict] = None,
        sort_by: Optional[str] = None,
        sort_order: str = "asc",
        page: int = 1,
        per_page: int = 50,
        max_records: Optional[int] = None,
        access_token: str = None,
    ) -> dict:
        """Fetch records using the official Zoho SDK with dynamic initialization"""

        # Dynamic initialization with access token
        if access_token:
            if not self._initialize_sdk_with_access_token(access_token):
                return {
                    "success": False,
                    "error": "SDK_INITIALIZATION_FAILED",
                    "error_type": "AuthenticationError",
                    "message": "Failed to initialize SDK with provided access token",
                    "records": [],
                    "total_count": 0,
                    "has_more": False,
                }
        elif not self.is_initialized:
            return {
                "success": False,
                "error": "SDK_NOT_INITIALIZED",
                "error_type": "InitializationError",
                "message": "Zoho SDK is not initialized. Please provide access token or complete OAuth authentication first.",
                "records": [],
                "total_count": 0,
                "has_more": False,
            }

        def _fetch_sync():
            try:
                record_operations = RecordOperations(module)
                param_instance = ParameterMap()

                # Configure fields
                if fields:
                    param_instance.add(GetRecordsParam.fields, ",".join(fields))

                # Configure pagination
                param_instance.add(GetRecordsParam.page, page)
                param_instance.add(
                    GetRecordsParam.per_page, min(per_page, 200)
                )  # Zoho max is 200

                # Configure sorting
                if sort_by:
                    sort_value = f"{sort_by}:{sort_order}"
                    param_instance.add(GetRecordsParam.sort_by, sort_value)

                # Make API call
                response = record_operations.get_records(param_instance)
                return self._process_fetch_response(response, max_records)

            except Exception as e:
                logging.error(f"Error in _fetch_sync: {str(e)}")
                return {
                    "success": False,
                    "error": "EXCEPTION",
                    "error_type": e.__class__.__name__,
                    "message": str(e),
                    "records": [],
                    "total_count": 0,
                    "has_more": False,
                }

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _fetch_sync)

    async def search_records(
        self,
        modules: List[str],
        search_text: str,
        fields: Optional[List[str]] = None,
        criteria: Optional[dict] = None,
        max_results: int = 100,
        access_token: str = None,
    ) -> dict:
        """Search across multiple modules with dynamic initialization"""

        # Dynamic initialization with access token
        if access_token:
            if not self._initialize_sdk_with_access_token(access_token):
                return {
                    "success": False,
                    "error": "SDK_INITIALIZATION_FAILED",
                    "error_type": "AuthenticationError",
                    "message": "Failed to initialize SDK with provided access token",
                    "results": {module: [] for module in modules},
                }
        elif not self.is_initialized:
            return {
                "success": False,
                "error": "SDK_NOT_INITIALIZED",
                "error_type": "InitializationError",
                "message": "Zoho SDK is not initialized. Please provide access token or complete OAuth authentication first.",
                "results": {module: [] for module in modules},
            }

        def _search_sync():
            results = {}

            for module in modules:
                try:
                    record_operations = RecordOperations(module)
                    param_instance = ParameterMap()

                    # Configure search
                    param_instance.add(SearchRecordsParam.criteria, f"({search_text})")

                    if fields:
                        param_instance.add(SearchRecordsParam.fields, ",".join(fields))

                    # Limit results per module
                    param_instance.add(
                        SearchRecordsParam.per_page,
                        min(max_results // len(modules), 200),
                    )

                    response = record_operations.search_records(param_instance)
                    module_results = self._process_search_response(response)
                    results[module] = module_results

                except Exception as e:
                    logging.warning(f"Error searching in module {module}: {str(e)}")
                    results[module] = []

            return results

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _search_sync)

    async def get_module_schema(self, module: str) -> dict:
        """Get complete module schema"""

        # Check if SDK is properly initialized
        if not self.is_initialized:
            return {
                "error": "SDK_NOT_INITIALIZED",
                "error_type": "InitializationError",
                "message": "Zoho SDK is not initialized. Please complete OAuth authentication first.",
                "module": module,
            }

        def _get_schema_sync():
            try:
                modules_operations = ModulesOperations()
                response = modules_operations.get_module(module)
                return self._process_module_response(response)
            except Exception as e:
                logging.error(f"Error getting schema for {module}: {str(e)}")
                return {
                    "error": "EXCEPTION",
                    "error_type": e.__class__.__name__,
                    "message": str(e),
                    "module": module,
                }

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _get_schema_sync)

    async def get_module_fields(self, module: str) -> List[dict]:
        """Get detailed field information for a module"""

        # Check if SDK is properly initialized
        if not self.is_initialized:
            return [
                {
                    "error": "SDK_NOT_INITIALIZED",
                    "error_type": "InitializationError",
                    "message": "Zoho SDK is not initialized. Please complete OAuth authentication first.",
                    "module": module,
                }
            ]

        def _get_fields_sync():
            try:
                fields_operations = FieldsOperations(module)
                response = fields_operations.get_fields()
                return self._process_fields_response(response)
            except Exception as e:
                logging.error(f"Error getting fields for {module}: {str(e)}")
                return [
                    {
                        "error": "EXCEPTION",
                        "error_type": e.__class__.__name__,
                        "message": str(e),
                        "module": module,
                    }
                ]

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _get_fields_sync)

    async def get_picklist_values(self, module: str, field: str) -> List[dict]:
        """Get picklist values for a specific field in a module"""

        # Check if SDK is properly initialized
        if not self.is_initialized:
            return [
                {
                    "error": "SDK_NOT_INITIALIZED",
                    "error_type": "InitializationError",
                    "message": "Zoho SDK is not initialized. Please complete OAuth authentication first.",
                    "module": module,
                    "field": field,
                }
            ]

        def _get_picklist_sync():
            try:
                fields_operations = FieldsOperations(module)
                response = fields_operations.get_field(field)
                return self._process_picklist_response(response)
            except Exception as e:
                logging.error(
                    f"Error getting picklist values for {module}.{field}: {str(e)}"
                )
                return [
                    {
                        "error": "EXCEPTION",
                        "error_type": e.__class__.__name__,
                        "message": str(e),
                        "module": module,
                        "field": field,
                    }
                ]

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _get_picklist_sync)

    def _process_create_response(self, response) -> dict:
        """Process create record response"""
        if response is not None:
            response_object = response.get_object()
            if isinstance(response_object, ActionWrapper):
                action_response = response_object.get_data()[0]
                if isinstance(action_response, SuccessResponse):
                    return {
                        "success": True,
                        "id": action_response.get_details().get("id"),
                        "message": action_response.get_message().get_value(),
                        "status": action_response.get_status().get_value(),
                        "details": action_response.get_details(),
                    }
                elif isinstance(action_response, APIException):
                    error_details = {
                        "success": False,
                        "error": (
                            action_response.get_code().get_value()
                            if action_response.get_code()
                            else "UNKNOWN_ERROR"
                        ),
                        "error_type": action_response.__class__.__name__,
                        "message": action_response.get_message().get_value(),
                    }

                    # Add additional error details if available
                    if (
                        hasattr(action_response, "get_details")
                        and action_response.get_details()
                    ):
                        error_details["details"] = action_response.get_details()

                    return error_details

        return {
            "success": False,
            "error": "INVALID_RESPONSE",
            "message": "Invalid response from Zoho CRM",
        }

    def _process_fetch_response(
        self, response, max_records: Optional[int] = None
    ) -> dict:
        """Process fetch records response"""
        if response is not None:
            response_object = response.get_object()
            if isinstance(response_object, ResponseWrapper):
                records = []
                for record in response_object.get_data():
                    record_dict = {}
                    for key, value in record.get_key_values().items():
                        record_dict[key] = value
                    records.append(record_dict)

                # Apply max_records limit if specified
                if max_records and len(records) > max_records:
                    records = records[:max_records]

                return {
                    "records": records,
                    "total_count": len(records),
                    "has_more": len(response_object.get_data())
                    == 200,  # Zoho max per page
                }
            elif isinstance(response_object, APIException):
                raise Exception(
                    f"API Error: {response_object.get_message().get_value()}"
                )

        return {"records": [], "total_count": 0, "has_more": False}

    def _process_search_response(self, response) -> List[dict]:
        """Process search records response"""
        records = []
        if response is not None:
            response_object = response.get_object()
            if isinstance(response_object, ResponseWrapper):
                for record in response_object.get_data():
                    record_dict = {}
                    for key, value in record.get_key_values().items():
                        record_dict[key] = value
                    records.append(record_dict)

        return records

    def _process_module_response(self, response) -> dict:
        """Process module schema response"""
        if response is not None:
            response_object = response.get_object()
            if hasattr(response_object, "get_modules"):
                module = response_object.get_modules()[0]
                return {
                    "api_name": module.get_api_name(),
                    "module_name": module.get_module_name(),
                    "plural_label": module.get_plural_label(),
                    "singular_label": module.get_singular_label(),
                    "creatable": module.get_creatable(),
                    "updatable": module.get_updatable(),
                    "deletable": module.get_deletable(),
                    "viewable": module.get_viewable(),
                }

        return {}

    def _process_fields_response(self, response) -> List[dict]:
        """Process fields response"""
        fields = []
        if response is not None:
            response_object = response.get_object()
            if hasattr(response_object, "get_fields"):
                for field in response_object.get_fields():
                    field_dict = {
                        "api_name": field.get_api_name(),
                        "field_label": field.get_field_label(),
                        "data_type": field.get_data_type(),
                        "mandatory": field.get_mandatory(),
                        "read_only": field.get_read_only(),
                        "custom_field": field.get_custom_field(),
                    }

                    # Add picklist values if applicable
                    if (
                        hasattr(field, "get_pick_list_values")
                        and field.get_pick_list_values()
                    ):
                        field_dict["pick_list_values"] = [
                            {
                                "display_value": pv.get_display_value(),
                                "actual_value": pv.get_actual_value(),
                            }
                            for pv in field.get_pick_list_values()
                        ]

                    fields.append(field_dict)

        return fields

    def _process_picklist_response(self, response) -> List[dict]:
        """Process picklist values response"""
        values = []
        if response is not None:
            response_object = response.get_object()
            if hasattr(response_object, "get_fields"):
                field = response_object.get_fields()[0]
                if (
                    hasattr(field, "get_pick_list_values")
                    and field.get_pick_list_values()
                ):
                    values = [
                        {
                            "display_value": pv.get_display_value(),
                            "actual_value": pv.get_actual_value(),
                        }
                        for pv in field.get_pick_list_values()
                    ]

        return values
