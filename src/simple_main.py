#!/usr/bin/env python3
"""
Simplified Zoho CRM MCP Server using REST APIs directly
Clean, lightweight implementation without SDK bloat
"""

import asyncio
import logging
import os
from typing import Dict, List, Optional, Any
import httpx
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel
from pydantic_settings import BaseSettings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ZohoSettings(BaseSettings):
    """Simple settings for Zoho CRM API"""

    zoho_client_id: str = ""
    zoho_client_secret: str = ""
    zoho_environment: str = "US"  # US, EU, IN, AU, CN

    # Optional fields from .env (ignored if not needed)
    server_name: str = "Simple Zoho CRM MCP Server"
    log_level: str = "INFO"
    rate_limit_requests: int = 100
    rate_limit_window: int = 60
    debug: bool = False

    class Config:
        env_file = ".env"
        extra = "ignore"  # Ignore extra fields in .env


class ZohoAPI:
    """Simple Zoho CRM REST API client"""

    def __init__(self, settings: ZohoSettings):
        self.settings = settings
        self.base_urls = {
            "US": "https://www.zohoapis.com/crm/v6",
            "EU": "https://www.zohoapis.eu/crm/v6",
            "IN": "https://www.zohoapis.in/crm/v6",
            "AU": "https://www.zohoapis.com.au/crm/v6",
            "CN": "https://www.zohoapis.com.cn/crm/v6",
        }
        self.base_url = self.base_urls.get(
            settings.zoho_environment, self.base_urls["US"]
        )

    def _get_headers(self, access_token: str) -> Dict[str, str]:
        """Get headers for API requests"""
        return {
            "Authorization": f"Zoho-oauthtoken {access_token}",
            "Content-Type": "application/json",
        }

    async def create_record(self, module: str, data: Dict, access_token: str) -> Dict:
        """Create a record using REST API"""
        url = f"{self.base_url}/{module}"
        headers = self._get_headers(access_token)
        payload = {"data": [data]}

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(url, json=payload, headers=headers)
                response.raise_for_status()
                result = response.json()

                if result.get("data") and len(result["data"]) > 0:
                    record = result["data"][0]
                    if record.get("code") == "SUCCESS":
                        return {
                            "success": True,
                            "id": record.get("details", {}).get("id"),
                            "message": record.get(
                                "message", "Record created successfully"
                            ),
                        }
                    else:
                        return {
                            "success": False,
                            "error": record.get("code", "UNKNOWN_ERROR"),
                            "message": record.get("message", "Failed to create record"),
                        }

                return {
                    "success": False,
                    "error": "INVALID_RESPONSE",
                    "message": "No data in response",
                }

            except httpx.HTTPStatusError as e:
                error_detail = ""
                try:
                    error_data = e.response.json()
                    error_detail = error_data.get("message", str(e))
                except:
                    error_detail = str(e)

                return {
                    "success": False,
                    "error": f"HTTP_{e.response.status_code}",
                    "message": f"API request failed: {error_detail}",
                }
            except Exception as e:
                return {"success": False, "error": "REQUEST_FAILED", "message": str(e)}

    async def get_records(
        self,
        module: str,
        access_token: str,
        fields: Optional[List[str]] = None,
        page: int = 1,
        per_page: int = 50,
    ) -> Dict:
        """Get records using REST API"""
        url = f"{self.base_url}/{module}"
        headers = self._get_headers(access_token)

        params = {"page": page, "per_page": min(per_page, 200)}  # Zoho max is 200

        if fields:
            params["fields"] = ",".join(fields)

        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, headers=headers, params=params)
                response.raise_for_status()
                result = response.json()

                records = result.get("data", [])
                return {
                    "success": True,
                    "records": records,
                    "count": len(records),
                    "page": page,
                    "per_page": per_page,
                }

            except httpx.HTTPStatusError as e:
                error_detail = ""
                try:
                    error_data = e.response.json()
                    error_detail = error_data.get("message", str(e))
                except:
                    error_detail = str(e)

                return {
                    "success": False,
                    "error": f"HTTP_{e.response.status_code}",
                    "message": f"API request failed: {error_detail}",
                    "records": [],
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": "REQUEST_FAILED",
                    "message": str(e),
                    "records": [],
                }

    async def search_records(
        self, module: str, criteria: str, access_token: str
    ) -> Dict:
        """Search records using REST API"""
        url = f"{self.base_url}/{module}/search"
        headers = self._get_headers(access_token)
        params = {"criteria": criteria}

        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, headers=headers, params=params)
                response.raise_for_status()
                result = response.json()

                records = result.get("data", [])
                return {"success": True, "records": records, "count": len(records)}

            except httpx.HTTPStatusError as e:
                if e.response.status_code == 204:
                    return {"success": True, "records": [], "count": 0}

                error_detail = ""
                try:
                    error_data = e.response.json()
                    error_detail = error_data.get("message", str(e))
                except:
                    error_detail = str(e)

                return {
                    "success": False,
                    "error": f"HTTP_{e.response.status_code}",
                    "message": f"Search failed: {error_detail}",
                    "records": [],
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": "SEARCH_FAILED",
                    "message": str(e),
                    "records": [],
                }


# Initialize settings and API client
settings = ZohoSettings()
zoho_api = ZohoAPI(settings)

# Initialize MCP Server
mcp = FastMCP("Simple Zoho CRM MCP Server")


def extract_access_token() -> Optional[str]:
    """Extract access token from FastMCP context or environment"""
    # Try to extract from FastMCP HTTP headers using the proper dependency
    try:
        from fastmcp.server.dependencies import get_http_headers

        # Get headers safely (returns empty dict if no request context)
        headers = get_http_headers()
        print(headers)

        # Extract from Authorization header
        auth_header = headers.get("authorization", "")
        if auth_header:
            # Handle "Bearer <token>" format
            if auth_header.startswith("Bearer "):
                token = auth_header[7:]  # Remove "Bearer " prefix
                logger.info("Bearer token extracted from Authorization header")
                return token
            # Handle direct token
            logger.info("Direct token extracted from Authorization header")
            return auth_header

    except Exception as e:
        logger.debug(f"Could not extract token from FastMCP context: {e}")

    logger.warning("No access token found in headers, environment, or refresh token")
    return None


@mcp.tool()
async def create_record(module: str, record_data: dict) -> dict:
    """
    Create a new record in Zoho CRM

    Args:
        module: CRM module name (e.g., 'Leads', 'Contacts', 'Accounts')
        record_data: Dictionary with field names and values

    Returns:
        Dictionary with creation result
    """
    access_token = extract_access_token()

    if not access_token:
        return {
            "success": False,
            "error": "MISSING_ACCESS_TOKEN",
            "message": "Access token is required. Provide it in the Authorization header as 'Bearer <token>' or set ZOHO_ACCESS_TOKEN environment variable.",
        }

    return await zoho_api.create_record(module, record_data, access_token)


@mcp.tool()
async def get_records(
    module: str,
    fields: Optional[List[str]] = None,
    page: int = 1,
    per_page: int = 50,
) -> dict:
    """
    Get records from Zoho CRM

    Args:
        module: CRM module name
        fields: List of fields to retrieve (optional)
        page: Page number (default: 1)
        per_page: Records per page (default: 50, max: 200)

    Returns:
        Dictionary with records and metadata
    """
    access_token = extract_access_token()

    if not access_token:
        return {
            "success": False,
            "error": "MISSING_ACCESS_TOKEN",
            "message": "Access token is required. Provide it in the Authorization header as 'Bearer <token>' or set ZOHO_ACCESS_TOKEN environment variable.",
            "records": [],
        }

    return await zoho_api.get_records(module, access_token, fields, page, per_page)


@mcp.tool()
async def search_records(module: str, search_criteria: str) -> dict:
    """
    Search records in Zoho CRM

    Args:
        module: CRM module name
        search_criteria: Search criteria (e.g., "(Email:equals:<EMAIL>)")

    Returns:
        Dictionary with search results
    """
    access_token = extract_access_token()

    if not access_token:
        return {
            "success": False,
            "error": "MISSING_ACCESS_TOKEN",
            "message": "Access token is required. Provide it via: 1) Authorization header as 'Bearer <token>', 2) ZOHO_ACCESS_TOKEN environment variable, or 3) ZOHO_REFRESH_TOKEN environment variable for automatic refresh.",
            "records": [],
        }

    return await zoho_api.search_records(module, search_criteria, access_token)


@mcp.tool()
async def health_check() -> dict:
    """Health check for the MCP server"""
    return {
        "status": "healthy",
        "service": "simple-zoho-crm-mcp",
        "environment": settings.zoho_environment,
        "base_url": zoho_api.base_url,
    }


if __name__ == "__main__":
    import sys

    logger.info("🚀 Starting Simple Zoho CRM MCP Server")
    logger.info(f"📍 Environment: {settings.zoho_environment}")
    logger.info(f"🌐 API Base URL: {zoho_api.base_url}")
    logger.info("💡 Authentication Options:")
    logger.info("   1. Authorization header: 'Bearer <token>'")
    logger.info("   2. Environment variable: ZOHO_ACCESS_TOKEN")
    logger.info("   3. Auto-refresh with: ZOHO_REFRESH_TOKEN")

    # Check command line arguments for transport type
    transport = "http"  # default
    port = 8000

    logger.info(f"🌐 Starting Streamable HTTP server on port {port}")
    logger.info(f"📡 Server will be available at http://localhost:{port}/mcp")
    logger.info(
        "🔗 Use this server with MCP clients that support Streamable HTTP transport (recommended)"
    )
    mcp.run(transport="streamable-http")
