"""
Pydantic models for the Zoho CRM MCP Server.
Defines data structures for request/response validation.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any
from datetime import datetime


class ZohoRecord(BaseModel):
    """Base model for Zoho CRM records"""

    id: Optional[str] = None
    created_time: Optional[datetime] = None
    modified_time: Optional[datetime] = None
    created_by: Optional[Dict[str, Any]] = None
    modified_by: Optional[Dict[str, Any]] = None


class CreateRecordRequest(BaseModel):
    """Request model for creating records"""

    module: str = Field(..., description="Zoho CRM module name")
    record_data: Dict[str, Any] = Field(..., description="Record field data")
    duplicate_check: bool = Field(True, description="Check for duplicates")
    trigger_workflows: bool = Field(False, description="Trigger workflows")
    approval_process: bool = Field(False, description="Trigger approval process")


class FetchRecordsRequest(BaseModel):
    """Request model for fetching records"""

    module: str = Field(..., description="Zoho CRM module name")
    fields: Optional[List[str]] = Field(None, description="Fields to retrieve")
    criteria: Optional[Dict[str, Any]] = Field(None, description="Filter criteria")
    sort_by: Optional[str] = Field(None, description="Sort field")
    sort_order: str = Field("asc", description="Sort order")
    page: int = Field(1, description="Page number", ge=1)
    per_page: int = Field(50, description="Records per page", ge=1, le=200)
    max_records: Optional[int] = Field(None, description="Maximum records to return")


class SearchRecordsRequest(BaseModel):
    """Request model for searching records"""

    modules: List[str] = Field(..., description="Modules to search in")
    search_text: str = Field(..., description="Search text")
    fields: Optional[List[str]] = Field(None, description="Fields to search in")
    criteria: Optional[Dict[str, Any]] = Field(None, description="Additional criteria")
    max_results: int = Field(100, description="Maximum results", ge=1, le=1000)


class MCPResponse(BaseModel):
    """Standard MCP response model"""

    success: bool
    data: Optional[Any] = None
    error: Optional[str] = None
    error_type: Optional[str] = None
    message: Optional[str] = None


class ZohoModuleSchema(BaseModel):
    """Model for Zoho module schema information"""

    api_name: str
    module_name: str
    plural_label: str
    singular_label: str
    creatable: bool
    updatable: bool
    deletable: bool
    viewable: bool


class ZohoField(BaseModel):
    """Model for Zoho field metadata"""

    api_name: str
    field_label: str
    data_type: str
    mandatory: bool
    read_only: bool
    custom_field: bool
    pick_list_values: Optional[List[Dict[str, str]]] = None
