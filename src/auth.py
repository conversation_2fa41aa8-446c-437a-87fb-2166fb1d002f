#!/usr/bin/env python3
"""
Authentication module for Zoho CRM MCP Server
Handles Bearer token extraction and validation.
"""

import logging
from typing import Optional, Dict, Any
from contextvars import ContextVar

from .config import SecurityConfig

logger = logging.getLogger(__name__)

# Context variable to store access token for the current request
access_token_context: ContextVar[str] = ContextVar("access_token")


class AuthenticationError(Exception):
    """Custom exception for authentication errors"""
    
    def __init__(self, message: str, code: str = "AUTHENTICATION_FAILED"):
        self.message = message
        self.code = code
        super().__init__(self.message)


class AuthHandler:
    """Authentication handler for Bearer token validation"""
    
    def __init__(self, security_config: SecurityConfig):
        self.config = security_config
        self.auth_header = security_config.auth_header_name
        self.auth_scheme = security_config.auth_scheme
        self.require_auth = security_config.require_auth
    
    def extract_token_from_headers(self, headers: Dict[str, str]) -> Optional[str]:
        """Extract Bearer token from request headers"""
        try:
            # Get authorization header (case-insensitive)
            auth_header = None
            for key, value in headers.items():
                if key.lower() == self.auth_header.lower():
                    auth_header = value
                    break
            
            if not auth_header:
                if self.require_auth:
                    logger.warning(f"Missing {self.auth_header} header")
                    raise AuthenticationError(
                        f"Missing {self.auth_header} header",
                        "MISSING_AUTH_HEADER"
                    )
                return None
            
            # Parse Bearer token
            parts = auth_header.split()
            if len(parts) != 2:
                logger.warning(f"Invalid {self.auth_header} header format")
                raise AuthenticationError(
                    f"Invalid {self.auth_header} header format. Expected: '{self.auth_scheme} <token>'",
                    "INVALID_AUTH_FORMAT"
                )
            
            scheme, token = parts
            if scheme.lower() != self.auth_scheme.lower():
                logger.warning(f"Invalid authentication scheme: {scheme}")
                raise AuthenticationError(
                    f"Invalid authentication scheme. Expected: {self.auth_scheme}",
                    "INVALID_AUTH_SCHEME"
                )
            
            if not token or token.strip() == "":
                logger.warning("Empty access token")
                raise AuthenticationError(
                    "Empty access token",
                    "EMPTY_TOKEN"
                )
            
            logger.debug(f"Successfully extracted {self.auth_scheme} token")
            return token.strip()
            
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Error extracting token: {e}")
            raise AuthenticationError(
                f"Error processing authentication: {str(e)}",
                "AUTH_PROCESSING_ERROR"
            )
    
    def validate_token_format(self, token: str) -> bool:
        """Basic token format validation"""
        if not token:
            return False
        
        # Basic checks for token format
        if len(token) < 10:  # Minimum reasonable token length
            return False
        
        # Check for obvious invalid tokens
        invalid_tokens = ["test", "demo", "example", "placeholder"]
        if token.lower() in invalid_tokens:
            return False
        
        return True
    
    def set_access_token(self, token: str) -> None:
        """Set the access token in the current context"""
        if not self.validate_token_format(token):
            logger.warning("Invalid token format detected")
            raise AuthenticationError(
                "Invalid token format",
                "INVALID_TOKEN_FORMAT"
            )
        
        access_token_context.set(token)
        logger.debug("Access token set in context")
    
    def get_access_token(self) -> str:
        """Get the access token from the current context"""
        try:
            token = access_token_context.get()
            if not token:
                raise AuthenticationError(
                    "No access token found in request context",
                    "NO_TOKEN_IN_CONTEXT"
                )
            return token
        except LookupError:
            raise AuthenticationError(
                "No access token found in request context",
                "NO_TOKEN_IN_CONTEXT"
            )
    
    def create_auth_error_response(self, error: AuthenticationError) -> Dict[str, Any]:
        """Create standardized authentication error response"""
        return {
            "success": False,
            "error": error.code,
            "message": error.message,
            "authentication_required": {
                "provider": "Zoho CRM",
                "auth_type": "Bearer Token",
                "header_name": self.auth_header,
                "header_format": f"{self.auth_scheme} <your_access_token>",
                "required_scopes": [
                    "ZohoCRM.modules.ALL",
                    "ZohoCRM.settings.READ"
                ],
                "instructions": [
                    "1. Obtain an access token from Zoho CRM OAuth flow",
                    f"2. Include it in the {self.auth_header} header",
                    f"3. Format: '{self.auth_header}: {self.auth_scheme} <your_token>'"
                ]
            }
        }
    
    def is_auth_required(self) -> bool:
        """Check if authentication is required"""
        return self.require_auth


def get_access_token() -> str:
    """Convenience function to get access token from context"""
    try:
        return access_token_context.get()
    except LookupError:
        raise AuthenticationError(
            "No access token found in request context",
            "NO_TOKEN_IN_CONTEXT"
        )


def set_access_token(token: str) -> None:
    """Convenience function to set access token in context"""
    access_token_context.set(token)
