"""
Zoho OAuth 2.1 provider implementation with PKCE support.
"""

import secrets
import hashlib
import base64
import urllib.parse
from typing import Dict, List, Optional
import logging

from .models import TokenInfo
from .exceptions import OAuthProviderError


class ZohoOAuthProvider:
    """Zoho OAuth 2.1 provider with PKCE support."""

    def __init__(
        self,
        client_id: str,
        client_secret: str,
        environment: str = "US",
        redirect_uri: str = "http://localhost:8000",
    ):
        self.client_id = client_id
        self.client_secret = client_secret
        self.environment = environment
        self.redirect_uri = redirect_uri

        # Map environment to OAuth endpoints
        self.domain_map = {
            "US": "https://accounts.zoho.com",
            "EU": "https://accounts.zoho.eu",
            "IN": "https://accounts.zoho.in",
        }

        self.base_domain = self.domain_map.get(environment, "https://accounts.zoho.com")
        self.auth_url = f"{self.base_domain}/oauth/v2/auth"
        self.token_url = f"{self.base_domain}/oauth/v2/token"
        self.introspection_url = f"{self.base_domain}/oauth/v2/token/info"

        self.logger = logging.getLogger(__name__)

    def generate_pkce_pair(self) -> Dict[str, str]:
        """
        Generate PKCE code verifier and challenge pair.

        Returns:
            Dictionary with 'code_verifier' and 'code_challenge'
        """
        # Generate code verifier (43-128 characters)
        code_verifier = (
            base64.urlsafe_b64encode(secrets.token_bytes(32))
            .decode("utf-8")
            .rstrip("=")
        )

        # Generate code challenge (SHA256 hash of verifier)
        code_challenge = (
            base64.urlsafe_b64encode(
                hashlib.sha256(code_verifier.encode("utf-8")).digest()
            )
            .decode("utf-8")
            .rstrip("=")
        )

        return {"code_verifier": code_verifier, "code_challenge": code_challenge}

    def get_authorization_url(
        self,
        scopes: Optional[List[str]] = None,
        state: Optional[str] = None,
        code_challenge: Optional[str] = None,
        redirect_uri: Optional[str] = None,
    ) -> str:
        """
        Generate OAuth 2.1 authorization URL with PKCE.

        Args:
            scopes: List of OAuth scopes to request
            state: CSRF protection state parameter
            code_challenge: PKCE code challenge
            redirect_uri: OAuth redirect URI

        Returns:
            Authorization URL string
        """
        if scopes is None:
            scopes = [
                "ZohoCRM.modules.ALL",
                "ZohoCRM.settings.ALL",
                "ZohoCRM.users.ALL",
                "ZohoCRM.org.ALL",
            ]

        if state is None:
            state = secrets.token_urlsafe(32)

        if redirect_uri is None:
            redirect_uri = self.redirect_uri

        params = {
            "response_type": "code",
            "client_id": self.client_id,
            "redirect_uri": redirect_uri,
            "scope": ",".join(scopes),
            "state": state,
            "access_type": "offline",  # Request refresh token
        }

        # Add PKCE parameters if provided
        if code_challenge:
            params.update(
                {"code_challenge": code_challenge, "code_challenge_method": "S256"}
            )

        auth_url = f"{self.auth_url}?" + urllib.parse.urlencode(params)

        self.logger.info(
            f"Generated authorization URL for environment: {self.environment}"
        )
        return auth_url

    def get_oauth_endpoints(self) -> Dict[str, str]:
        """Get OAuth endpoint URLs for the configured environment."""
        return {
            "authorization": self.auth_url,
            "token": self.token_url,
            "introspection": self.introspection_url,
        }

    def get_required_scopes(self) -> List[str]:
        """Get the list of required OAuth scopes for Zoho CRM access."""
        return [
            "ZohoCRM.modules.ALL",
            "ZohoCRM.settings.ALL",
            "ZohoCRM.users.ALL",
            "ZohoCRM.org.ALL",
        ]

    def create_authorization_request(self) -> Dict[str, str]:
        """
        Create a complete authorization request with PKCE.

        Returns:
            Dictionary containing:
            - authorization_url: URL to redirect user to
            - state: CSRF protection state
            - code_verifier: PKCE code verifier (store securely)
            - code_challenge: PKCE code challenge
        """
        # Generate PKCE pair
        pkce_pair = self.generate_pkce_pair()

        # Generate state for CSRF protection
        state = secrets.token_urlsafe(32)

        # Generate authorization URL
        authorization_url = self.get_authorization_url(
            state=state, code_challenge=pkce_pair["code_challenge"]
        )

        return {
            "authorization_url": authorization_url,
            "state": state,
            "code_verifier": pkce_pair["code_verifier"],
            "code_challenge": pkce_pair["code_challenge"],
        }

    def validate_callback_state(self, received_state: str, expected_state: str) -> bool:
        """
        Validate the state parameter from OAuth callback.

        Args:
            received_state: State received from OAuth callback
            expected_state: Expected state value

        Returns:
            True if states match, False otherwise
        """
        return secrets.compare_digest(received_state, expected_state)

    def get_provider_info(self) -> Dict[str, any]:
        """Get provider information for authentication requirements."""
        return {
            "provider": "zoho",
            "auth_type": "bearer",
            "header_name": "Authorization",
            "header_format": "Bearer {access_token}",
            "required_scopes": self.get_required_scopes(),
            "token_source": "access_token",
            "oauth_endpoints": self.get_oauth_endpoints(),
            "environment": self.environment,
        }

    async def exchange_code_for_token(
        self,
        authorization_code: str,
        redirect_uri: str,
        code_verifier: str,
    ) -> Dict[str, any]:
        """
        Exchange authorization code for access token using PKCE.

        Args:
            authorization_code: Authorization code from OAuth callback
            redirect_uri: Redirect URI used in authorization request
            code_verifier: PKCE code verifier

        Returns:
            Dictionary containing token information

        Raises:
            OAuthProviderError: If token exchange fails
        """
        import httpx

        token_data = {
            "grant_type": "authorization_code",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "redirect_uri": redirect_uri,
            "code": authorization_code,
            "code_verifier": code_verifier,
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.token_url,
                    data=token_data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"},
                )

                if response.status_code != 200:
                    error_data = (
                        response.json()
                        if response.headers.get("content-type", "").startswith(
                            "application/json"
                        )
                        else {"error": response.text}
                    )
                    raise OAuthProviderError(
                        f"Token exchange failed: {error_data.get('error', 'Unknown error')}"
                    )

                token_info = response.json()
                self.logger.info(
                    "Successfully exchanged authorization code for access token"
                )

                return token_info

        except httpx.RequestError as e:
            raise OAuthProviderError(f"Network error during token exchange: {str(e)}")
        except Exception as e:
            raise OAuthProviderError(f"Token exchange failed: {str(e)}")
