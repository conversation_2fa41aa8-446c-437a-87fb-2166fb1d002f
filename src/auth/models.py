"""
Data models for authentication system.
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from datetime import datetime


@dataclass
class TokenInfo:
    """Information about a validated token."""

    access_token: str
    is_valid: bool
    expires_at: Optional[datetime] = None
    scopes: List[str] = None
    user_id: Optional[str] = None
    client_id: Optional[str] = None

    def __post_init__(self):
        if self.scopes is None:
            self.scopes = []


@dataclass
class AuthRequirement:
    """Authentication requirement specification."""

    provider: str
    auth_type: str
    header_name: str
    header_format: str
    required_scopes: List[str]
    token_source: str
    authorization_url: Optional[str] = None
    oauth_endpoints: Optional[Dict[str, str]] = None


@dataclass
class AuthError:
    """Structured authentication error response."""

    code: str
    message: str
    authentication_requirements: List[AuthRequirement]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format for JSON response."""
        return {
            "error": {
                "code": self.code,
                "message": self.message,
                "details": {
                    "authentication_requirements": [
                        {
                            "provider": req.provider,
                            "auth_type": req.auth_type,
                            "header_name": req.header_name,
                            "header_format": req.header_format,
                            "required_scopes": req.required_scopes,
                            "token_source": req.token_source,
                            "authorization_url": req.authorization_url,
                            "oauth_endpoints": req.oauth_endpoints,
                        }
                        for req in self.authentication_requirements
                    ]
                },
            }
        }


def create_zoho_auth_error(
    authorization_url: str = "/auth/authorize", environment: str = "US"
) -> AuthError:
    """Create a standard Zoho authentication error response."""

    # Map environment to OAuth endpoints
    domain_map = {
        "US": "https://accounts.zoho.com",
        "EU": "https://accounts.zoho.eu",
        "IN": "https://accounts.zoho.in",
    }

    base_domain = domain_map.get(environment, "https://accounts.zoho.com")

    oauth_endpoints = {
        "authorization": f"{base_domain}/oauth/v2/auth",
        "token": f"{base_domain}/oauth/v2/token",
        "introspection": f"{base_domain}/oauth/v2/token/info",
    }

    requirement = AuthRequirement(
        provider="zoho",
        auth_type="bearer",
        header_name="Authorization",
        header_format="Bearer {access_token}",
        required_scopes=[
            "ZohoCRM.modules.ALL",
            "ZohoCRM.settings.ALL",
            "ZohoCRM.users.ALL",
            "ZohoCRM.org.ALL",
        ],
        token_source="access_token",
        authorization_url=authorization_url,
        oauth_endpoints=oauth_endpoints,
    )

    return AuthError(
        code="AUTHENTICATION_REQUIRED",
        message="Authentication required to access this resource",
        authentication_requirements=[requirement],
    )
