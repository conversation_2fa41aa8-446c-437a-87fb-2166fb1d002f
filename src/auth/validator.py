"""
Token validation logic for Zoho OAuth tokens.
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import httpx

from .models import TokenInfo
from .exceptions import (
    TokenValidationError,
    TokenExpiredError,
    InsufficientScopesError,
    OAuthProviderError,
)


class TokenValidator:
    """Validates Zoho OAuth tokens using introspection endpoint."""

    def __init__(
        self,
        client_id: str,
        client_secret: str,
        environment: str = "US",
        cache_ttl: int = 300,  # 5 minutes
    ):
        self.client_id = client_id
        self.client_secret = client_secret
        self.environment = environment
        self.cache_ttl = cache_ttl

        # Simple in-memory cache for validation results
        self._validation_cache: Dict[str, Dict[str, Any]] = {}

        # Map environment to introspection endpoint
        self.domain_map = {
            "US": "https://accounts.zoho.com",
            "EU": "https://accounts.zoho.eu",
            "IN": "https://accounts.zoho.in",
        }

        self.introspection_url = f"{self.domain_map.get(environment, 'https://accounts.zoho.com')}/oauth/v2/token/info"

        self.logger = logging.getLogger(__name__)

    async def validate_token(
        self, access_token: str, required_scopes: Optional[List[str]] = None
    ) -> TokenInfo:
        """
        Validate an access token and return token information.

        Args:
            access_token: The access token to validate
            required_scopes: List of required scopes (optional)

        Returns:
            TokenInfo object with validation results

        Raises:
            TokenValidationError: If token validation fails
            TokenExpiredError: If token has expired
            InsufficientScopesError: If token lacks required scopes
        """
        # Check cache first
        cached_result = self._get_cached_validation(access_token)
        if cached_result:
            token_info = TokenInfo(**cached_result)
            if required_scopes:
                self._check_scopes(token_info, required_scopes)
            return token_info

        # Validate with Zoho introspection endpoint
        try:
            token_info = await self._introspect_token(access_token)

            # Cache the result if valid
            if token_info.is_valid:
                self._cache_validation_result(access_token, token_info)

            # Check required scopes
            if required_scopes:
                self._check_scopes(token_info, required_scopes)

            return token_info

        except Exception as e:
            self.logger.error(f"Token validation failed: {str(e)}")
            if isinstance(
                e, (TokenValidationError, TokenExpiredError, InsufficientScopesError)
            ):
                raise
            raise TokenValidationError(f"Token validation error: {str(e)}")

    async def _introspect_token(self, access_token: str) -> TokenInfo:
        """Introspect token with Zoho OAuth endpoint."""

        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(
                    self.introspection_url,
                    params={"access_token": access_token},
                    timeout=10.0,
                )

                if response.status_code != 200:
                    raise OAuthProviderError(
                        f"Introspection request failed: {response.status_code}",
                        {
                            "status_code": response.status_code,
                            "response": response.text,
                        },
                    )

                token_data = response.json()

                # Check if token is active/valid
                if not token_data.get("active", False):
                    return TokenInfo(access_token=access_token, is_valid=False)

                # Parse expiration time
                expires_at = None
                if "exp" in token_data:
                    expires_at = datetime.fromtimestamp(token_data["exp"])
                elif "expires_in" in token_data:
                    expires_at = datetime.now() + timedelta(
                        seconds=token_data["expires_in"]
                    )

                # Check if token is expired
                if expires_at and expires_at <= datetime.now():
                    raise TokenExpiredError("Token has expired")

                # Parse scopes
                scopes = []
                if "scope" in token_data:
                    if isinstance(token_data["scope"], str):
                        scopes = token_data["scope"].split()
                    elif isinstance(token_data["scope"], list):
                        scopes = token_data["scope"]

                return TokenInfo(
                    access_token=access_token,
                    is_valid=True,
                    expires_at=expires_at,
                    scopes=scopes,
                    user_id=token_data.get("sub") or token_data.get("user_id"),
                    client_id=token_data.get("client_id"),
                )

            except httpx.RequestError as e:
                raise OAuthProviderError(
                    f"Network error during token introspection: {str(e)}"
                )
            except Exception as e:
                if isinstance(e, (TokenExpiredError, OAuthProviderError)):
                    raise
                raise TokenValidationError(f"Token introspection failed: {str(e)}")

    def _check_scopes(self, token_info: TokenInfo, required_scopes: List[str]) -> None:
        """Check if token has required scopes."""
        if not token_info.is_valid:
            raise TokenValidationError("Cannot check scopes on invalid token")

        token_scopes = set(token_info.scopes or [])
        required_scopes_set = set(required_scopes)

        # Check if all required scopes are present
        missing_scopes = required_scopes_set - token_scopes
        if missing_scopes:
            raise InsufficientScopesError(
                f"Token missing required scopes: {', '.join(missing_scopes)}",
                required_scopes=required_scopes,
                token_scopes=list(token_scopes),
            )

    def _get_cached_validation(self, access_token: str) -> Optional[Dict[str, Any]]:
        """Get cached validation result if still valid."""
        cache_key = self._get_cache_key(access_token)

        if cache_key in self._validation_cache:
            cached_data = self._validation_cache[cache_key]

            # Check if cache entry is still valid
            if time.time() - cached_data["cached_at"] < self.cache_ttl:
                return cached_data["token_info"]
            else:
                # Remove expired cache entry
                del self._validation_cache[cache_key]

        return None

    def _cache_validation_result(
        self, access_token: str, token_info: TokenInfo
    ) -> None:
        """Cache validation result."""
        cache_key = self._get_cache_key(access_token)

        self._validation_cache[cache_key] = {
            "token_info": {
                "access_token": token_info.access_token,
                "is_valid": token_info.is_valid,
                "expires_at": token_info.expires_at,
                "scopes": token_info.scopes,
                "user_id": token_info.user_id,
                "client_id": token_info.client_id,
            },
            "cached_at": time.time(),
        }

        # Simple cache cleanup - remove old entries if cache gets too large
        if len(self._validation_cache) > 1000:
            self._cleanup_cache()

    def _get_cache_key(self, access_token: str) -> str:
        """Generate cache key for access token."""
        # Use last 8 characters of token as key (for security)
        return f"token_{access_token[-8:]}"

    def _cleanup_cache(self) -> None:
        """Remove expired entries from cache."""
        current_time = time.time()
        expired_keys = [
            key
            for key, data in self._validation_cache.items()
            if current_time - data["cached_at"] >= self.cache_ttl
        ]

        for key in expired_keys:
            del self._validation_cache[key]

        self.logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
