"""
Authentication endpoints for OAuth flow and token management.
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, Request, Query, HTTPException
from fastapi.responses import JSONResponse, RedirectResponse

from .zoho_provider import ZohoOAuthProvider
from .validator import TokenValidator
from .models import create_zoho_auth_error
from .exceptions import AuthenticationError


class AuthEndpoints:
    """Authentication endpoints for OAuth 2.1 flow."""

    def __init__(
        self,
        client_id: str,
        client_secret: str,
        environment: str = "US",
        redirect_uri: str = "http://localhost:8000/auth/callback",
    ):
        self.provider = ZohoOAuthProvider(
            client_id=client_id,
            client_secret=client_secret,
            environment=environment,
            redirect_uri=redirect_uri,
        )

        self.validator = TokenValidator(
            client_id=client_id, client_secret=client_secret, environment=environment
        )

        self.logger = logging.getLogger(__name__)

        # Create router
        self.router = APIRouter(prefix="/auth", tags=["authentication"])
        self._setup_routes()

    def _setup_routes(self):
        """Setup authentication routes."""

        @self.router.get("/authorize")
        async def get_authorization_url(
            redirect_uri: Optional[str] = Query(None, description="OAuth redirect URI"),
            scopes: Optional[str] = Query(
                None, description="Comma-separated list of scopes"
            ),
        ) -> Dict[str, Any]:
            """
            Generate OAuth 2.1 authorization URL with PKCE.

            This endpoint creates a complete OAuth authorization request including:
            - Authorization URL with PKCE challenge
            - State parameter for CSRF protection
            - Code verifier (store this securely on client side)
            """
            try:
                # Parse scopes if provided
                scope_list = None
                if scopes:
                    scope_list = [s.strip() for s in scopes.split(",")]

                # Create authorization request
                auth_request = self.provider.create_authorization_request()

                # Override redirect URI if provided
                if redirect_uri:
                    auth_request["authorization_url"] = (
                        self.provider.get_authorization_url(
                            scopes=scope_list,
                            state=auth_request["state"],
                            code_challenge=auth_request["code_challenge"],
                            redirect_uri=redirect_uri,
                        )
                    )

                self.logger.info("Generated OAuth authorization URL")

                return {
                    "authorization_url": auth_request["authorization_url"],
                    "state": auth_request["state"],
                    "code_verifier": auth_request["code_verifier"],
                    "instructions": {
                        "step_1": "Visit the authorization_url in your browser",
                        "step_2": "Grant the requested permissions",
                        "step_3": "You will be redirected back with an authorization code",
                        "step_4": "Use the authorization code to exchange for access tokens",
                        "note": "Store the code_verifier securely - you'll need it for token exchange",
                    },
                }

            except Exception as e:
                self.logger.error(f"Error generating authorization URL: {str(e)}")
                raise HTTPException(
                    status_code=500, detail="Failed to generate authorization URL"
                )

        @self.router.get("/callback")
        async def oauth_callback(
            code: Optional[str] = Query(None, description="Authorization code"),
            state: Optional[str] = Query(None, description="State parameter"),
            error: Optional[str] = Query(None, description="OAuth error"),
            error_description: Optional[str] = Query(
                None, description="OAuth error description"
            ),
        ) -> Dict[str, Any]:
            """
            Handle OAuth callback from Zoho.

            This is a basic callback handler that returns the authorization code.
            In a real application, you would exchange this code for tokens.
            """
            if error:
                self.logger.error(f"OAuth error: {error} - {error_description}")
                raise HTTPException(
                    status_code=400,
                    detail=f"OAuth authorization failed: {error_description or error}",
                )

            if not code:
                raise HTTPException(
                    status_code=400, detail="Authorization code not provided"
                )

            if not state:
                raise HTTPException(
                    status_code=400, detail="State parameter not provided"
                )

            return {
                "message": "Authorization code received successfully",
                "authorization_code": code,
                "state": state,
                "next_steps": {
                    "step_1": "Exchange this authorization code for access tokens",
                    "step_2": "Use the access token in Authorization header: 'Bearer <token>'",
                    "note": "Authorization codes expire quickly - exchange immediately",
                },
            }

        @self.router.post("/validate")
        async def validate_token(request: Request) -> Dict[str, Any]:
            """
            Validate an access token.

            Expects Authorization header with Bearer token.
            """
            try:
                # Extract token from Authorization header
                auth_header = request.headers.get("Authorization")
                if not auth_header or not auth_header.startswith("Bearer "):
                    raise HTTPException(
                        status_code=401,
                        detail="Authorization header with Bearer token required",
                    )

                access_token = auth_header[7:]  # Remove "Bearer " prefix

                # Validate token
                token_info = await self.validator.validate_token(access_token)

                return {
                    "valid": token_info.is_valid,
                    "expires_at": (
                        token_info.expires_at.isoformat()
                        if token_info.expires_at
                        else None
                    ),
                    "scopes": token_info.scopes,
                    "user_id": token_info.user_id,
                    "client_id": token_info.client_id,
                }

            except Exception as e:
                self.logger.error(f"Token validation error: {str(e)}")
                return {"valid": False, "error": str(e)}

        @self.router.get("/requirements")
        async def get_auth_requirements() -> Dict[str, Any]:
            """
            Get authentication requirements for this server.

            Returns the structured authentication requirements that clients
            will receive in 401 error responses.
            """
            auth_error = create_zoho_auth_error(
                authorization_url="/auth/authorize",
                environment=self.provider.environment,
            )

            return auth_error.to_dict()

        @self.router.get("/provider-info")
        async def get_provider_info() -> Dict[str, Any]:
            """Get OAuth provider information."""
            return self.provider.get_provider_info()

        @self.router.get("/health")
        async def auth_health_check() -> Dict[str, str]:
            """Health check for authentication system."""
            return {
                "status": "healthy",
                "provider": "zoho",
                "environment": self.provider.environment,
            }


def create_auth_router(
    client_id: str,
    client_secret: str,
    environment: str = "US",
    redirect_uri: str = "http://localhost:8000/auth/callback",
) -> APIRouter:
    """Create authentication router with configured endpoints."""

    auth_endpoints = AuthEndpoints(
        client_id=client_id,
        client_secret=client_secret,
        environment=environment,
        redirect_uri=redirect_uri,
    )

    return auth_endpoints.router
