"""
Authentication module for Zoho CRM MCP Server.

This module provides OAuth 2.1 compliant authentication with structured error responses
and stateless token validation for the Zoho CRM MCP server.
"""

from .middleware import ZohoAuthMiddleware
from .zoho_provider import ZohoOAuthProvider
from .validator import TokenValidator
from .models import TokenInfo, AuthError
from .exceptions import AuthenticationError, TokenValidationError
from .endpoints import create_auth_router

__all__ = [
    "ZohoAuthMiddleware",
    "ZohoOAuthProvider",
    "TokenValidator",
    "TokenInfo",
    "AuthError",
    "AuthenticationError",
    "TokenValidationError",
    "create_auth_router",
]
