"""
FastMCP authentication middleware for Zoho OAuth.
"""

import logging
from typing import Optional, List, Callable, Any
from fastapi import Request, Response, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse

from .validator import TokenValidator
from .models import create_zoho_auth_error, TokenInfo
from .exceptions import (
    AuthenticationError,
    TokenValidationError,
    TokenExpiredError,
    InsufficientScopesError,
    MissingAuthHeaderError,
    InvalidAuthHeaderError,
)


class ZohoAuthMiddleware:
    """FastMCP middleware for Zoho OAuth 2.1 authentication."""

    def __init__(
        self,
        client_id: str,
        client_secret: str,
        environment: str = "US",
        required_scopes: Optional[List[str]] = None,
        cache_ttl: int = 300,
        exclude_paths: Optional[List[str]] = None,
    ):
        self.client_id = client_id
        self.client_secret = client_secret
        self.environment = environment
        self.required_scopes = required_scopes or [
            "ZohoCRM.modules.ALL",
            "ZohoCRM.settings.ALL",
            "ZohoCRM.users.ALL",
            "ZohoCRM.org.ALL",
        ]

        # Initialize token validator
        self.validator = TokenValidator(
            client_id=client_id,
            client_secret=client_secret,
            environment=environment,
            cache_ttl=cache_ttl,
        )

        # Paths that don't require authentication
        self.exclude_paths = exclude_paths or [
            "/auth/authorize",
            "/auth/callback",
            "/auth/requirements",
            "/health",
            "/docs",
            "/openapi.json",
        ]

        self.logger = logging.getLogger(__name__)

    async def __call__(self, request: Request, call_next: Callable) -> Response:
        """Process request through authentication middleware."""

        # Skip authentication for excluded paths
        if self._should_skip_auth(request.url.path):
            return await call_next(request)

        try:
            # Extract and validate token
            token_info = await self._authenticate_request(request)

            # Add token info to request state for downstream use
            request.state.token_info = token_info
            request.state.authenticated = True

            # Continue with request
            response = await call_next(request)
            return response

        except AuthenticationError as e:
            return self._create_auth_error_response(e)
        except Exception as e:
            self.logger.error(f"Unexpected error in auth middleware: {str(e)}")
            return self._create_auth_error_response(
                AuthenticationError("Authentication system error")
            )

    async def _authenticate_request(self, request: Request) -> TokenInfo:
        """Extract and validate authentication from request."""

        # Extract Bearer token from Authorization header
        access_token = self._extract_bearer_token(request)

        # Validate token with Zoho
        token_info = await self.validator.validate_token(
            access_token=access_token, required_scopes=self.required_scopes
        )

        if not token_info.is_valid:
            raise TokenValidationError("Invalid access token")

        self.logger.debug(
            f"Successfully authenticated request for user: {token_info.user_id}"
        )
        return token_info

    def _extract_bearer_token(self, request: Request) -> str:
        """Extract Bearer token from Authorization header."""

        auth_header = request.headers.get("Authorization")
        if not auth_header:
            raise MissingAuthHeaderError("Authorization header is required")

        # Check for Bearer token format
        if not auth_header.startswith("Bearer "):
            raise InvalidAuthHeaderError(
                "Authorization header must use Bearer token format: 'Bearer <token>'"
            )

        # Extract token
        token = auth_header[7:]  # Remove "Bearer " prefix
        if not token.strip():
            raise InvalidAuthHeaderError("Bearer token cannot be empty")

        return token.strip()

    def _should_skip_auth(self, path: str) -> bool:
        """Check if path should skip authentication."""
        return any(path.startswith(excluded) for excluded in self.exclude_paths)

    def _create_auth_error_response(self, error: AuthenticationError) -> JSONResponse:
        """Create structured authentication error response."""

        # Create appropriate error response based on error type
        if isinstance(error, MissingAuthHeaderError):
            auth_error = create_zoho_auth_error(
                authorization_url="/auth/authorize", environment=self.environment
            )
            return JSONResponse(status_code=401, content=auth_error.to_dict())

        elif isinstance(error, InvalidAuthHeaderError):
            auth_error = create_zoho_auth_error(
                authorization_url="/auth/authorize", environment=self.environment
            )
            # Modify message to be more specific
            auth_error.message = f"Invalid authorization header format. {error.message}"
            return JSONResponse(status_code=401, content=auth_error.to_dict())

        elif isinstance(error, TokenExpiredError):
            auth_error = create_zoho_auth_error(
                authorization_url="/auth/authorize", environment=self.environment
            )
            auth_error.code = "TOKEN_EXPIRED"
            auth_error.message = "Access token has expired. Please re-authenticate."
            return JSONResponse(status_code=401, content=auth_error.to_dict())

        elif isinstance(error, InsufficientScopesError):
            auth_error = create_zoho_auth_error(
                authorization_url="/auth/authorize", environment=self.environment
            )
            auth_error.code = "INSUFFICIENT_SCOPES"
            auth_error.message = f"Token missing required scopes. Required: {', '.join(error.required_scopes)}"
            return JSONResponse(status_code=403, content=auth_error.to_dict())

        elif isinstance(error, TokenValidationError):
            auth_error = create_zoho_auth_error(
                authorization_url="/auth/authorize", environment=self.environment
            )
            auth_error.code = "INVALID_TOKEN"
            auth_error.message = "Invalid or malformed access token"
            return JSONResponse(status_code=401, content=auth_error.to_dict())

        else:
            # Generic authentication error
            auth_error = create_zoho_auth_error(
                authorization_url="/auth/authorize", environment=self.environment
            )
            auth_error.message = error.message
            return JSONResponse(status_code=401, content=auth_error.to_dict())


def get_token_info(request: Request) -> Optional[TokenInfo]:
    """Helper function to get token info from authenticated request."""
    return getattr(request.state, "token_info", None)


def is_authenticated(request: Request) -> bool:
    """Helper function to check if request is authenticated."""
    return getattr(request.state, "authenticated", False)


def require_scopes(required_scopes: List[str]) -> Callable:
    """Decorator to require specific scopes for an endpoint."""

    def decorator(func: Callable) -> Callable:
        async def wrapper(request: Request, *args, **kwargs):
            token_info = get_token_info(request)
            if not token_info:
                raise HTTPException(status_code=401, detail="Authentication required")

            token_scopes = set(token_info.scopes or [])
            required_scopes_set = set(required_scopes)

            missing_scopes = required_scopes_set - token_scopes
            if missing_scopes:
                raise HTTPException(
                    status_code=403,
                    detail=f"Missing required scopes: {', '.join(missing_scopes)}",
                )

            return await func(request, *args, **kwargs)

        return wrapper

    return decorator
