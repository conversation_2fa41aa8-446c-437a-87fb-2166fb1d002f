"""
Authentication-related exceptions.
"""

from typing import Optional, Dict, Any


class AuthenticationError(Exception):
    """Base authentication error."""

    def __init__(self, message: str, error_code: str = "AUTHENTICATION_ERROR"):
        super().__init__(message)
        self.message = message
        self.error_code = error_code


class TokenValidationError(AuthenticationError):
    """Token validation failed."""

    def __init__(
        self,
        message: str = "Token validation failed",
        error_code: str = "INVALID_TOKEN",
        token_info: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, error_code)
        self.token_info = token_info or {}


class TokenExpiredError(TokenValidationError):
    """Token has expired."""

    def __init__(self, message: str = "Token has expired"):
        super().__init__(message, "TOKEN_EXPIRED")


class InsufficientScopesError(TokenValidationError):
    """Token does not have required scopes."""

    def __init__(
        self,
        message: str = "Token does not have required scopes",
        required_scopes: Optional[list] = None,
        token_scopes: Optional[list] = None,
    ):
        super().__init__(message, "INSUFFICIENT_SCOPES")
        self.required_scopes = required_scopes or []
        self.token_scopes = token_scopes or []


class MissingAuthHeaderError(AuthenticationError):
    """Authorization header is missing."""

    def __init__(self, message: str = "Authorization header is missing"):
        super().__init__(message, "MISSING_AUTH_HEADER")


class InvalidAuthHeaderError(AuthenticationError):
    """Authorization header format is invalid."""

    def __init__(self, message: str = "Invalid authorization header format"):
        super().__init__(message, "INVALID_AUTH_HEADER")


class OAuthProviderError(AuthenticationError):
    """OAuth provider communication error."""

    def __init__(
        self,
        message: str = "OAuth provider error",
        provider_response: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, "OAUTH_PROVIDER_ERROR")
        self.provider_response = provider_response or {}
