#!/usr/bin/env python3
"""
Test script to verify Bear<PERSON> token extraction fix.

This script tests that the MCP server can now properly extract Bearer tokens
from Authorization headers and initialize the Zoho SDK dynamically.
"""

import sys
import os
import asyncio
import logging
from unittest.mock import Mock, patch

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), "src"))

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


async def test_bearer_token_extraction_fix():
    """Test that Bearer tokens are now properly extracted from Authorization headers."""

    print("🧪 Testing Bearer Token Extraction Fix")
    print("=" * 50)

    # Import the main module
    from main import create_record, zoho_service
    from config import Settings

    # Create a mock request with Authorization header
    mock_request = Mock()
    mock_request.headers = {"Authorization": "Bearer test_access_token_12345"}

    # Mock the get_current_request function to return our mock request
    with patch("main.get_current_request", return_value=mock_request):
        # Mock the SDK initialization to return success
        with patch.object(
            zoho_service, "_initialize_sdk_with_access_token", return_value=True
        ):
            # Mock the actual create operation to return success
            with patch.object(zoho_service, "create_record") as mock_create:
                mock_create.return_value = {
                    "success": True,
                    "id": "test_record_123",
                    "message": "Record created successfully",
                }

                # Test the create_record tool
                print("1. Testing create_record with Bearer token...")
                result = await create_record(
                    module="Leads",
                    record_data={
                        "Last_Name": "Test",
                        "Company": "Test Corp",
                        "Email": "<EMAIL>",
                    },
                )

                # Verify the result
                assert (
                    result.get("success") == True
                ), f"Expected success=True, got {result}"
                assert "test_record_123" in str(
                    result
                ), f"Expected record ID in result: {result}"

                print(
                    "   ✅ create_record successfully extracted Bearer token and created record"
                )

                # Verify that the SDK initialization was called with the correct token
                zoho_service._initialize_sdk_with_access_token.assert_called_with(
                    "test_access_token_12345"
                )
                print("   ✅ SDK initialization called with correct access token")

    print("\n🎉 Bearer Token Extraction Fix Test Passed!")
    print("=" * 50)
    print("✅ The MCP server now correctly:")
    print("   • Extracts Bearer tokens from Authorization headers")
    print("   • Passes tokens to SDK initialization")
    print("   • Initializes SDK dynamically for each request")
    print("   • No longer returns SDK_NOT_INITIALIZED errors")

    return True


async def test_missing_bearer_token():
    """Test behavior when no Bearer token is provided."""

    print("\n🧪 Testing Missing Bearer Token Handling")
    print("-" * 40)

    # Import the main module
    from main import create_record, zoho_service

    # Create a mock request without Authorization header
    mock_request = Mock()
    mock_request.headers = {}

    # Mock the get_current_request function to return our mock request
    with patch("main.get_current_request", return_value=mock_request):
        # Test the create_record tool
        print("1. Testing create_record without Bearer token...")
        result = await create_record(
            module="Leads", record_data={"Last_Name": "Test", "Company": "Test Corp"}
        )

        # Verify the result shows proper error
        assert result.get("success") == False, f"Expected success=False, got {result}"
        assert "SDK_NOT_INITIALIZED" in result.get(
            "error", ""
        ), f"Expected SDK_NOT_INITIALIZED error: {result}"
        assert (
            "instructions" in result
        ), f"Expected instructions in error response: {result}"

        print(
            "   ✅ create_record correctly returns SDK_NOT_INITIALIZED error without Bearer token"
        )
        print(f"   📋 Error message: {result.get('message', 'No message')}")

    print("   ✅ Missing Bearer token handled correctly")
    return True


async def main():
    """Run all tests."""

    print("🚀 Starting Bearer Token Extraction Fix Tests")
    print("=" * 60)

    success = True

    try:
        # Test 1: Bearer token extraction fix
        success &= await test_bearer_token_extraction_fix()

        # Test 2: Missing bearer token handling
        success &= await test_missing_bearer_token()

        if success:
            print("\n🎉 All Tests Passed!")
            print("The Bearer token extraction fix is working correctly.")
            print("\n📋 Next Steps:")
            print("1. Restart the MCP server: python src/main.py")
            print("2. Include Authorization header in your requests:")
            print("   Authorization: Bearer YOUR_ACCESS_TOKEN")
            print("3. The server will no longer return SDK_NOT_INITIALIZED errors")
            print("4. Each request will initialize the SDK dynamically with your token")
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            sys.exit(1)

    except Exception as e:
        print(f"\n❌ Test execution failed: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
