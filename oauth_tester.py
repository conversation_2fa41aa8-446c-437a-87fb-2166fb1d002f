import requests
import json
from typing import Dict, Optional


class ZohoCRMAPITester:
    def __init__(self, access_token: str, api_domain: str = "https://www.zohoapis.com"):
        """
        Initialize the Zoho CRM API tester.

        Args:
            access_token: Your Zoho CRM access token
            api_domain: API domain (default for international accounts)
        """
        self.access_token = access_token
        self.api_domain = api_domain
        self.headers = {
            "Authorization": f"Zoho-oauthtoken {access_token}",
            "Content-Type": "application/json",
        }

    def test_token_validity(self) -> bool:
        """
        Test if the access token is valid by making a simple API call.

        Returns:
            <PERSON><PERSON>an indicating if token is valid
        """
        url = f"{self.api_domain}/crm/v6/org"

        try:
            response = requests.get(url, headers=self.headers)

            if response.status_code == 200:
                print("✅ Token is valid!")
                org_data = response.json()
                if "org" in org_data:
                    print(
                        f"Organization: {org_data['org'][0].get('company_name', 'N/A')}"
                    )
                return True
            else:
                print(f"❌ Token validation failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error testing token: {e}")
            return False

    def get_leads(
        self, fields: str = None, converted: bool = None, per_page: int = 5
    ) -> Dict:
        """
        Get leads from Zoho CRM.

        Args:
            fields: Comma-separated list of fields to retrieve
            converted: Filter by converted status
            per_page: Number of records per page

        Returns:
            API response as dictionary
        """
        url = f"{self.api_domain}/crm/v6/Leads"

        params = {}
        if fields:
            params["fields"] = fields
        if converted is not None:
            params["converted"] = str(converted).lower()
        if per_page:
            params["per_page"] = per_page

        try:
            response = requests.get(url, headers=self.headers, params=params)

            print(f"Request URL: {response.url}")
            print(f"Status Code: {response.status_code}")

            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ API call failed: {response.status_code}")
                print(f"Response: {response.text}")
                return {"error": response.text, "status_code": response.status_code}

        except Exception as e:
            print(f"❌ Error making API call: {e}")
            return {"error": str(e)}

    def get_user_info(self) -> Dict:
        """
        Get current user information.

        Returns:
            User information dictionary
        """
        url = f"{self.api_domain}/crm/v6/users?type=CurrentUser"

        try:
            response = requests.get(url, headers=self.headers)

            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to get user info: {response.status_code}")
                print(f"Response: {response.text}")
                return {"error": response.text}

        except Exception as e:
            print(f"❌ Error getting user info: {e}")
            return {"error": str(e)}


def main():
    """
    Main function to test Zoho CRM API.
    """
    # Get access token from user
    access_token = input("Enter your Zoho CRM access token: ").strip()

    # Initialize API tester
    api_tester = ZohoCRMAPITester(access_token)

    print("Testing Zoho CRM API...")
    print("=" * 50)

    # Test 1: Validate token
    print("\n1. Testing token validity...")
    if not api_tester.test_token_validity():
        print("❌ Token is invalid. Please generate a new token.")
        return

    # Test 2: Get user info
    print("\n2. Getting user information...")
    user_info = api_tester.get_user_info()
    if "users" in user_info:
        user = user_info["users"][0]
        print(f"✅ User: {user.get('full_name', 'N/A')} ({user.get('email', 'N/A')})")

    # Test 3: Get leads (your original query)
    print("\n3. Getting leads...")
    fields = "Last_Name,Email,Record_Status__s,Converted__s,Converted_Date_Time"
    leads_data = api_tester.get_leads(fields=fields, converted=True, per_page=5)

    if "data" in leads_data:
        print(f"✅ Found {len(leads_data['data'])} leads")
        for i, lead in enumerate(leads_data["data"], 1):
            print(f"  {i}. {lead.get('Last_Name', 'N/A')} - {lead.get('Email', 'N/A')}")
    else:
        print("❌ No leads found or error occurred")
        if "error" in leads_data:
            print(f"Error: {leads_data['error']}")


def generate_curl_command():
    """
    Generate the correct curl command format.
    """
    print("\nCorrect curl command format:")
    print("=" * 50)

    # Single line version
    print("Single line version:")
    curl_single = '''curl "https://www.zohoapis.com/crm/v6/Leads?fields=Last_Name,Email,Record_Status__s,Converted__s,Converted_Date_Time&converted=true&per_page=5" -X GET -H "Authorization: Zoho-oauthtoken YOUR_TOKEN_HERE"'''
    print(curl_single)

    print("\nMulti-line version (with backslashes):")
    curl_multi = '''curl "https://www.zohoapis.com/crm/v6/Leads?fields=Last_Name,Email,Record_Status__s,Converted__s,Converted_Date_Time&converted=true&per_page=5" \\
-X GET \\
-H "Authorization: Zoho-oauthtoken YOUR_TOKEN_HERE"'''
    print(curl_multi)


if __name__ == "__main__":
    choice = input(
        "Choose option:\n1. Test API with Python\n2. Show correct curl format\nEnter choice (1 or 2): "
    ).strip()

    if choice == "1":
        main()
    elif choice == "2":
        generate_curl_command()
    else:
        print("Invalid choice. Running API test...")
        main()
