#!/usr/bin/env python3
"""
Test script for dynamic SDK initialization architecture
Tests the new Bearer token-based initialization pattern
"""

import asyncio
import logging
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), "src"))

from zoho_service import ZohoCRMService
from config import Settings

# Configure logging
logging.basicConfig(level=logging.DEBUG)


async def test_dynamic_initialization():
    """Test the dynamic initialization architecture"""
    print("🧪 Testing Dynamic SDK Initialization Architecture")
    print("=" * 60)

    # Initialize settings
    settings = Settings()

    # Create service in dynamic mode (skip SDK init)
    print("1. Creating ZohoCRMService in dynamic mode...")
    service = ZohoCRMService(settings, skip_sdk_init=True)

    # Verify initial state
    print(f"   ✓ Service created")
    print(f"   ✓ is_initialized: {service.is_initialized}")
    print(f"   ✓ _current_access_token: {service._current_access_token}")
    print()

    # Test 1: Call without access token (should return error)
    print("2. Testing create_record without access token...")
    result = await service.create_record(
        module="Leads", record_data={"Last_Name": "Test", "Company": "Test Corp"}
    )
    print(f"   ✓ Result: {result.get('error', 'No error')}")
    print(f"   ✓ Message: {result.get('message', 'No message')}")
    print()

    # Test 2: Call with invalid access token (should fail gracefully)
    print("3. Testing create_record with invalid access token...")
    result = await service.create_record(
        module="Leads",
        record_data={"Last_Name": "Test", "Company": "Test Corp"},
        access_token="invalid_token_123",
    )
    print(f"   ✓ Result: {result.get('error', 'No error')}")
    print(f"   ✓ Message: {result.get('message', 'No message')}")
    print()

    # Test 3: Test access token extraction
    print("4. Testing access token extraction...")
    test_header = "Bearer test_access_token_12345"
    extracted = service._extract_access_token_from_auth_header(test_header)
    print(f"   ✓ Input: {test_header}")
    print(f"   ✓ Extracted: {extracted}")
    print()

    # Test 4: Test fetch_records without token
    print("5. Testing fetch_records without access token...")
    result = await service.fetch_records(module="Leads")
    print(f"   ✓ Result: {result.get('error', 'No error')}")
    print(f"   ✓ Message: {result.get('message', 'No message')}")
    print()

    # Test 5: Test search_records without token
    print("6. Testing search_records without access token...")
    result = await service.search_records(
        modules=["Leads", "Contacts"], search_text="test"
    )
    print(f"   ✓ Result: {result.get('error', 'No error')}")
    print(f"   ✓ Message: {result.get('message', 'No message')}")
    print()

    print("🎉 Dynamic initialization architecture tests completed!")
    print("=" * 60)
    print("✅ Architecture Summary:")
    print("   • Server starts without SDK initialization")
    print("   • SDK is initialized dynamically per request using Bearer tokens")
    print("   • Access tokens are extracted from Authorization headers")
    print("   • Multiple users can use different tokens via switch_user()")
    print("   • Graceful error handling when tokens are missing/invalid")
    print()
    print("📋 Usage Instructions:")
    print("   1. Start the MCP server: python src/main.py")
    print("   2. Include Authorization header in requests:")
    print("      Authorization: Bearer YOUR_ACCESS_TOKEN")
    print("   3. The SDK will initialize automatically for each request")


if __name__ == "__main__":
    asyncio.run(test_dynamic_initialization())
