[project]
name = "github-mcp"
version = "0.1.0"
description = "GitHub MCP server with 31 GitHub API tools for repositories, issues, pull requests, commits, branches, and more"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "mcp>=1.9.4",
    "requests>=2.31.0",
    "starlette>=0.37.2",
    "uvicorn>=0.25.0",
    "python-dotenv>=1.0.0",
]

[project.scripts]
github-mcp = "src:main"
