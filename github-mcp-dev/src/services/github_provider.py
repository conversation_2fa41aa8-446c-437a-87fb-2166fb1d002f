import requests
import json
import base64
from typing import Optional, Dict, Any, List
from helper.logger import logging

class GitHubService:
    def __init__(self, access_token: str):
        """Initialize GitHub service with personal access token."""
        self.access_token = access_token
        self.base_url = "https://api.github.com"
        self.headers = {
            "Authorization": f"token {access_token}",
            "Accept": "application/vnd.github+json",
            "X-GitHub-Api-Version": "2022-11-28"
        }
        
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make HTTP request to GitHub API."""
        try:
            url = f"{self.base_url}{endpoint}"
            response = requests.request(
                method=method,
                url=url,
                headers=self.headers,
                json=data,
                params=params
            )
            
            if response.status_code in [200, 201, 204, 202]:
                return {
                    "success": True,
                    "data": response.json() if response.content else {},
                    "status_code": response.status_code
                }
            else:
                return {
                    "success": False,
                    "error": f"GitHub API error: {response.status_code} - {response.text}",
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Request failed: {str(e)}"
            }

    # Repository methods
    def search_repositories(self, query: str, sort: Optional[str] = None, order: Optional[str] = None, 
                           per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """Search for GitHub repositories."""
        params = {
            "q": query,
            "per_page": per_page,
            "page": page
        }
        if sort:
            params["sort"] = sort
        if order:
            params["order"] = order
            
        return self._make_request("GET", "/search/repositories", params=params)

    def get_repository(self, owner: str, repo: str) -> Dict[str, Any]:
        """Get detailed information about a GitHub repository."""
        return self._make_request("GET", f"/repos/{owner}/{repo}")

    def create_repository(self, name: str, description: Optional[str] = None, 
                         private: bool = False, auto_init: bool = False) -> Dict[str, Any]:
        """Create a new GitHub repository."""
        data = {
            "name": name,
            "private": private,
            "auto_init": auto_init
        }
        if description:
            data["description"] = description
            
        return self._make_request("POST", "/user/repos", data=data)

    def fork_repository(self, owner: str, repo: str, organization: Optional[str] = None) -> Dict[str, Any]:
        """Fork a GitHub repository."""
        data = {}
        if organization:
            data["organization"] = organization
            
        return self._make_request("POST", f"/repos/{owner}/{repo}/forks", data=data)

    # Code search methods
    def search_code(self, query: str, sort: Optional[str] = None, order: Optional[str] = None,
                   per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """Search for code across GitHub repositories."""
        params = {
            "q": query,
            "per_page": per_page,
            "page": page
        }
        if sort:
            params["sort"] = sort
        if order:
            params["order"] = order
            
        return self._make_request("GET", "/search/code", params=params)

    def get_file_contents(self, owner: str, repo: str, path: str, ref: Optional[str] = None) -> Dict[str, Any]:
        """Get the contents of a file from a GitHub repository."""
        params = {}
        if ref:
            params["ref"] = ref
            
        return self._make_request("GET", f"/repos/{owner}/{repo}/contents/{path}", params=params)

    def create_or_update_file(self, owner: str, repo: str, path: str, message: str, 
                             content: str, sha: Optional[str] = None, branch: Optional[str] = None) -> Dict[str, Any]:
        """Create or update a single file in a GitHub repository."""
        # Encode content in base64
        encoded_content = base64.b64encode(content.encode()).decode()
        
        data = {
            "message": message,
            "content": encoded_content
        }
        if sha:
            data["sha"] = sha
        if branch:
            data["branch"] = branch
            
        return self._make_request("PUT", f"/repos/{owner}/{repo}/contents/{path}", data=data)

    def push_files(self, owner: str, repo: str, branch: str, message: str, files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Push multiple files to a GitHub repository in a single commit."""
        # This requires getting the latest commit, creating a tree, and creating a new commit
        # First get the latest commit
        commit_response = self._make_request("GET", f"/repos/{owner}/{repo}/git/refs/heads/{branch}")
        if not commit_response["success"]:
            return commit_response
            
        latest_commit_sha = commit_response["data"]["object"]["sha"]
        
        # Get the base tree
        commit_info = self._make_request("GET", f"/repos/{owner}/{repo}/git/commits/{latest_commit_sha}")
        if not commit_info["success"]:
            return commit_info
            
        base_tree_sha = commit_info["data"]["tree"]["sha"]
        
        # Create tree items
        tree_items = []
        for file_info in files:
            tree_items.append({
                "path": file_info["path"],
                "mode": "100644",
                "type": "blob",
                "content": file_info["content"]
            })
        
        # Create new tree
        tree_data = {
            "base_tree": base_tree_sha,
            "tree": tree_items
        }
        tree_response = self._make_request("POST", f"/repos/{owner}/{repo}/git/trees", data=tree_data)
        if not tree_response["success"]:
            return tree_response
            
        new_tree_sha = tree_response["data"]["sha"]
        
        # Create new commit
        commit_data = {
            "message": message,
            "tree": new_tree_sha,
            "parents": [latest_commit_sha]
        }
        commit_response = self._make_request("POST", f"/repos/{owner}/{repo}/git/commits", data=commit_data)
        if not commit_response["success"]:
            return commit_response
            
        new_commit_sha = commit_response["data"]["sha"]
        
        # Update branch reference
        ref_data = {
            "sha": new_commit_sha
        }
        return self._make_request("PATCH", f"/repos/{owner}/{repo}/git/refs/heads/{branch}", data=ref_data)

    # User methods
    def search_users(self, query: str, sort: Optional[str] = None, order: Optional[str] = None,
                    per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """Search for GitHub users."""
        params = {
            "q": query,
            "per_page": per_page,
            "page": page
        }
        if sort:
            params["sort"] = sort
        if order:
            params["order"] = order
            
        return self._make_request("GET", "/search/users", params=params)

    # Issue methods
    def get_issue(self, owner: str, repo: str, issue_number: int) -> Dict[str, Any]:
        """Get details of a specific issue."""
        return self._make_request("GET", f"/repos/{owner}/{repo}/issues/{issue_number}")

    def add_issue_comment(self, owner: str, repo: str, issue_number: int, body: str) -> Dict[str, Any]:
        """Add a comment to a specific issue."""
        data = {"body": body}
        return self._make_request("POST", f"/repos/{owner}/{repo}/issues/{issue_number}/comments", data=data)

    def search_issues(self, query: str, sort: Optional[str] = None, order: Optional[str] = None,
                     per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """Search for issues in GitHub repositories."""
        params = {
            "q": query,
            "per_page": per_page,
            "page": page
        }
        if sort:
            params["sort"] = sort
        if order:
            params["order"] = order
            
        return self._make_request("GET", "/search/issues", params=params)

    def create_issue(self, owner: str, repo: str, title: str, body: Optional[str] = None,
                    assignees: Optional[List[str]] = None, labels: Optional[List[str]] = None) -> Dict[str, Any]:
        """Create a new issue in a GitHub repository."""
        data = {"title": title}
        if body:
            data["body"] = body
        if assignees:
            data["assignees"] = assignees
        if labels:
            data["labels"] = labels
            
        return self._make_request("POST", f"/repos/{owner}/{repo}/issues", data=data)

    def list_issues(self, owner: str, repo: str, state: str = "open", sort: str = "created",
                   direction: str = "desc", per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """List issues in a GitHub repository."""
        params = {
            "state": state,
            "sort": sort,
            "direction": direction,
            "per_page": per_page,
            "page": page
        }
        return self._make_request("GET", f"/repos/{owner}/{repo}/issues", params=params)

    def update_issue(self, owner: str, repo: str, issue_number: int, title: Optional[str] = None,
                    body: Optional[str] = None, state: Optional[str] = None,
                    assignees: Optional[List[str]] = None, labels: Optional[List[str]] = None) -> Dict[str, Any]:
        """Update an existing issue."""
        data = {}
        if title:
            data["title"] = title
        if body:
            data["body"] = body
        if state:
            data["state"] = state
        if assignees:
            data["assignees"] = assignees
        if labels:
            data["labels"] = labels
            
        return self._make_request("PATCH", f"/repos/{owner}/{repo}/issues/{issue_number}", data=data)

    def get_issue_comments(self, owner: str, repo: str, issue_number: int,
                          per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """Get comments for a specific issue."""
        params = {
            "per_page": per_page,
            "page": page
        }
        return self._make_request("GET", f"/repos/{owner}/{repo}/issues/{issue_number}/comments", params=params)

    # Commit methods
    def get_commit(self, owner: str, repo: str, ref: str) -> Dict[str, Any]:
        """Get details for a commit from a GitHub repository."""
        return self._make_request("GET", f"/repos/{owner}/{repo}/commits/{ref}")

    def list_commits(self, owner: str, repo: str, sha: Optional[str] = None, path: Optional[str] = None,
                    author: Optional[str] = None, since: Optional[str] = None, until: Optional[str] = None,
                    per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """Get list of commits of a branch in a GitHub repository."""
        params = {
            "per_page": per_page,
            "page": page
        }
        if sha:
            params["sha"] = sha
        if path:
            params["path"] = path
        if author:
            params["author"] = author
        if since:
            params["since"] = since
        if until:
            params["until"] = until
            
        return self._make_request("GET", f"/repos/{owner}/{repo}/commits", params=params)

    # Branch methods
    def list_branches(self, owner: str, repo: str, protected: Optional[bool] = None,
                     per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """List branches in a GitHub repository."""
        params = {
            "per_page": per_page,
            "page": page
        }
        if protected is not None:
            params["protected"] = protected
            
        return self._make_request("GET", f"/repos/{owner}/{repo}/branches", params=params)

    def create_branch(self, owner: str, repo: str, branch: str, from_branch: str = "main") -> Dict[str, Any]:
        """Create a new branch in a GitHub repository."""
        # First get the SHA of the from_branch
        ref_response = self._make_request("GET", f"/repos/{owner}/{repo}/git/refs/heads/{from_branch}")
        if not ref_response["success"]:
            return ref_response
            
        from_sha = ref_response["data"]["object"]["sha"]
        
        # Create new branch
        data = {
            "ref": f"refs/heads/{branch}",
            "sha": from_sha
        }
        return self._make_request("POST", f"/repos/{owner}/{repo}/git/refs", data=data)

    # Tag methods
    def list_tags(self, owner: str, repo: str, per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """List git tags in a GitHub repository."""
        params = {
            "per_page": per_page,
            "page": page
        }
        return self._make_request("GET", f"/repos/{owner}/{repo}/tags", params=params)

    def get_tag(self, owner: str, repo: str, tag: str) -> Dict[str, Any]:
        """Get details about a specific git tag."""
        return self._make_request("GET", f"/repos/{owner}/{repo}/git/tags/{tag}")

    # Pull request methods
    def get_pull_request(self, owner: str, repo: str, pull_number: int) -> Dict[str, Any]:
        """Get details of a specific pull request."""
        return self._make_request("GET", f"/repos/{owner}/{repo}/pulls/{pull_number}")

    def update_pull_request(self, owner: str, repo: str, pull_number: int, title: Optional[str] = None,
                           body: Optional[str] = None, state: Optional[str] = None,
                           base: Optional[str] = None) -> Dict[str, Any]:
        """Update an existing pull request."""
        data = {}
        if title:
            data["title"] = title
        if body:
            data["body"] = body
        if state:
            data["state"] = state
        if base:
            data["base"] = base
            
        return self._make_request("PATCH", f"/repos/{owner}/{repo}/pulls/{pull_number}", data=data)

    def list_pull_requests(self, owner: str, repo: str, state: str = "open", head: Optional[str] = None,
                          base: Optional[str] = None, sort: str = "created", direction: str = "desc",
                          per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """List pull requests in a GitHub repository."""
        params = {
            "state": state,
            "sort": sort,
            "direction": direction,
            "per_page": per_page,
            "page": page
        }
        if head:
            params["head"] = head
        if base:
            params["base"] = base
            
        return self._make_request("GET", f"/repos/{owner}/{repo}/pulls", params=params)

    def merge_pull_request(self, owner: str, repo: str, pull_number: int,
                          commit_title: Optional[str] = None, commit_message: Optional[str] = None,
                          merge_method: str = "merge") -> Dict[str, Any]:
        """Merge a pull request."""
        data = {"merge_method": merge_method}
        if commit_title:
            data["commit_title"] = commit_title
        if commit_message:
            data["commit_message"] = commit_message
            
        return self._make_request("PUT", f"/repos/{owner}/{repo}/pulls/{pull_number}/merge", data=data)

    def get_pull_request_files(self, owner: str, repo: str, pull_number: int,
                              per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """Get the files changed in a specific pull request."""
        params = {
            "per_page": per_page,
            "page": page
        }
        return self._make_request("GET", f"/repos/{owner}/{repo}/pulls/{pull_number}/files", params=params)

    def get_pull_request_status(self, owner: str, repo: str, pull_number: int) -> Dict[str, Any]:
        """Get the status of a specific pull request."""
        # Get PR details which includes status information
        pr_response = self._make_request("GET", f"/repos/{owner}/{repo}/pulls/{pull_number}")
        if not pr_response["success"]:
            return pr_response
            
        pr_data = pr_response["data"]
        
        # Get status checks for the PR
        status_response = self._make_request("GET", f"/repos/{owner}/{repo}/commits/{pr_data['head']['sha']}/status")
        
        return {
            "success": True,
            "data": {
                "pull_request": pr_data,
                "status_checks": status_response.get("data", {}) if status_response["success"] else {}
            }
        }

    def update_pull_request_branch(self, owner: str, repo: str, pull_number: int) -> Dict[str, Any]:
        """Update the branch of a pull request with the latest changes from the base branch."""
        return {
            "success": False,
            "error": "This feature is not implemented yet"
        }

    def get_pull_request_comments(self, owner: str, repo: str, pull_number: int,
                                 per_page: int = 30, page: int = 1) -> Dict[str, Any]:
        """Get comments for a specific pull request."""
        params = {
            "per_page": per_page,
            "page": page
        }
        return self._make_request("GET", f"/repos/{owner}/{repo}/pulls/{pull_number}/comments", params=params)

    def create_pull_request(self, owner: str, repo: str, title: str, head: str, base: str,
                           body: Optional[str] = None, draft: bool = False) -> Dict[str, Any]:
        """Create a new pull request."""
        data = {
            "title": title,
            "head": head,
            "base": base,
            "draft": draft
        }
        if body:
            data["body"] = body
            
        return self._make_request("POST", f"/repos/{owner}/{repo}/pulls", data=data)


# Export functions for use in server.py
def search_repositories(access_token: str, query: str, sort: Optional[str] = None, order: Optional[str] = None,
                       per_page: int = 30, page: int = 1) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.search_repositories(query, sort, order, per_page, page)

def get_repository(access_token: str, owner: str, repo: str) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.get_repository(owner, repo)

def create_repository(access_token: str, name: str, description: Optional[str] = None,
                     private: bool = False, auto_init: bool = False) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.create_repository(name, description, private, auto_init)

def fork_repository(access_token: str, owner: str, repo: str, organization: Optional[str] = None) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.fork_repository(owner, repo, organization)

def search_code(access_token: str, query: str, sort: Optional[str] = None, order: Optional[str] = None,
               per_page: int = 30, page: int = 1) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.search_code(query, sort, order, per_page, page)

def get_file_contents(access_token: str, owner: str, repo: str, path: str, ref: Optional[str] = None) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.get_file_contents(owner, repo, path, ref)

def create_or_update_file(access_token: str, owner: str, repo: str, path: str, message: str,
                         content: str, sha: Optional[str] = None, branch: Optional[str] = None) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.create_or_update_file(owner, repo, path, message, content, sha, branch)

def push_files(access_token: str, owner: str, repo: str, branch: str, message: str, files: List[Dict[str, Any]]) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.push_files(owner, repo, branch, message, files)

def search_users(access_token: str, query: str, sort: Optional[str] = None, order: Optional[str] = None,
                per_page: int = 30, page: int = 1) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.search_users(query, sort, order, per_page, page)

def get_issue(access_token: str, owner: str, repo: str, issue_number: int) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.get_issue(owner, repo, issue_number)

def add_issue_comment(access_token: str, owner: str, repo: str, issue_number: int, body: str) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.add_issue_comment(owner, repo, issue_number, body)

def search_issues(access_token: str, query: str, sort: Optional[str] = None, order: Optional[str] = None,
                 per_page: int = 30, page: int = 1) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.search_issues(query, sort, order, per_page, page)

def create_issue(access_token: str, owner: str, repo: str, title: str, body: Optional[str] = None,
                assignees: Optional[List[str]] = None, labels: Optional[List[str]] = None) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.create_issue(owner, repo, title, body, assignees, labels)

def list_issues(access_token: str, owner: str, repo: str, state: str = "open", sort: str = "created",
               direction: str = "desc", per_page: int = 30, page: int = 1) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.list_issues(owner, repo, state, sort, direction, per_page, page)

def update_issue(access_token: str, owner: str, repo: str, issue_number: int, title: Optional[str] = None,
                body: Optional[str] = None, state: Optional[str] = None,
                assignees: Optional[List[str]] = None, labels: Optional[List[str]] = None) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.update_issue(owner, repo, issue_number, title, body, state, assignees, labels)

def get_issue_comments(access_token: str, owner: str, repo: str, issue_number: int,
                      per_page: int = 30, page: int = 1) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.get_issue_comments(owner, repo, issue_number, per_page, page)

def get_commit(access_token: str, owner: str, repo: str, ref: str) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.get_commit(owner, repo, ref)

def list_commits(access_token: str, owner: str, repo: str, sha: Optional[str] = None, path: Optional[str] = None,
                author: Optional[str] = None, since: Optional[str] = None, until: Optional[str] = None,
                per_page: int = 30, page: int = 1) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.list_commits(owner, repo, sha, path, author, since, until, per_page, page)

def list_branches(access_token: str, owner: str, repo: str, protected: Optional[bool] = None,
                 per_page: int = 30, page: int = 1) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.list_branches(owner, repo, protected, per_page, page)

def create_branch(access_token: str, owner: str, repo: str, branch: str, from_branch: str = "main") -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.create_branch(owner, repo, branch, from_branch)

def list_tags(access_token: str, owner: str, repo: str, per_page: int = 30, page: int = 1) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.list_tags(owner, repo, per_page, page)

def get_tag(access_token: str, owner: str, repo: str, tag: str) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.get_tag(owner, repo, tag)

def get_pull_request(access_token: str, owner: str, repo: str, pull_number: int) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.get_pull_request(owner, repo, pull_number)

def update_pull_request(access_token: str, owner: str, repo: str, pull_number: int, title: Optional[str] = None,
                       body: Optional[str] = None, state: Optional[str] = None,
                       base: Optional[str] = None) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.update_pull_request(owner, repo, pull_number, title, body, state, base)

def list_pull_requests(access_token: str, owner: str, repo: str, state: str = "open", head: Optional[str] = None,
                      base: Optional[str] = None, sort: str = "created", direction: str = "desc",
                      per_page: int = 30, page: int = 1) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.list_pull_requests(owner, repo, state, head, base, sort, direction, per_page, page)

def merge_pull_request(access_token: str, owner: str, repo: str, pull_number: int,
                      commit_title: Optional[str] = None, commit_message: Optional[str] = None,
                      merge_method: str = "merge") -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.merge_pull_request(owner, repo, pull_number, commit_title, commit_message, merge_method)

def get_pull_request_files(access_token: str, owner: str, repo: str, pull_number: int,
                          per_page: int = 30, page: int = 1) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.get_pull_request_files(owner, repo, pull_number, per_page, page)

def get_pull_request_status(access_token: str, owner: str, repo: str, pull_number: int) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.get_pull_request_status(owner, repo, pull_number)

def update_pull_request_branch(access_token: str, owner: str, repo: str, pull_number: int) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.update_pull_request_branch(owner, repo, pull_number)

def get_pull_request_comments(access_token: str, owner: str, repo: str, pull_number: int,
                             per_page: int = 30, page: int = 1) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.get_pull_request_comments(owner, repo, pull_number, per_page, page)

def create_pull_request(access_token: str, owner: str, repo: str, title: str, head: str, base: str,
                       body: Optional[str] = None, draft: bool = False) -> Dict[str, Any]:
    service = GitHubService(access_token)
    return service.create_pull_request(owner, repo, title, head, base, body, draft)