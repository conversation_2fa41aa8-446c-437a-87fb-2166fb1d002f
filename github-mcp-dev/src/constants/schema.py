from pydantic import BaseModel
from typing import Optional, List, Dict, Any

# Repository schemas
class SearchRepositories(BaseModel):
    query: str
    sort: Optional[str] = None
    order: Optional[str] = None
    per_page: Optional[int] = 30
    page: Optional[int] = 1

class GetRepository(BaseModel):
    owner: str
    repo: str

class CreateRepository(BaseModel):
    name: str
    description: Optional[str] = None
    private: Optional[bool] = False
    auto_init: Optional[bool] = False

class ForkRepository(BaseModel):
    owner: str
    repo: str
    organization: Optional[str] = None

# Code search schemas
class SearchCode(BaseModel):
    query: str
    sort: Optional[str] = None
    order: Optional[str] = None
    per_page: Optional[int] = 30
    page: Optional[int] = 1

class GetFileContents(BaseModel):
    owner: str
    repo: str
    path: str
    ref: Optional[str] = None

class CreateOrUpdateFile(BaseModel):
    owner: str
    repo: str
    path: str
    message: str
    content: str
    sha: Optional[str] = None
    branch: Optional[str] = None

class PushFiles(BaseModel):
    owner: str
    repo: str
    branch: str
    message: str
    files: List[Dict[str, Any]]

# User schemas
class SearchUsers(BaseModel):
    query: str
    sort: Optional[str] = None
    order: Optional[str] = None
    per_page: Optional[int] = 30
    page: Optional[int] = 1

# Issue schemas
class GetIssue(BaseModel):
    owner: str
    repo: str
    issue_number: int

class AddIssueComment(BaseModel):
    owner: str
    repo: str
    issue_number: int
    body: str

class SearchIssues(BaseModel):
    query: str
    sort: Optional[str] = None
    order: Optional[str] = None
    per_page: Optional[int] = 30
    page: Optional[int] = 1

class CreateIssue(BaseModel):
    owner: str
    repo: str
    title: str
    body: Optional[str] = None
    assignees: Optional[List[str]] = None
    labels: Optional[List[str]] = None

class ListIssues(BaseModel):
    owner: str
    repo: str
    state: Optional[str] = "open"
    sort: Optional[str] = "created"
    direction: Optional[str] = "desc"
    per_page: Optional[int] = 30
    page: Optional[int] = 1

class UpdateIssue(BaseModel):
    owner: str
    repo: str
    issue_number: int
    title: Optional[str] = None
    body: Optional[str] = None
    state: Optional[str] = None
    assignees: Optional[List[str]] = None
    labels: Optional[List[str]] = None

class GetIssueComments(BaseModel):
    owner: str
    repo: str
    issue_number: int
    per_page: Optional[int] = 30
    page: Optional[int] = 1

# Commit schemas
class GetCommit(BaseModel):
    owner: str
    repo: str
    ref: str

class ListCommits(BaseModel):
    owner: str
    repo: str
    sha: Optional[str] = None
    path: Optional[str] = None
    author: Optional[str] = None
    since: Optional[str] = None
    until: Optional[str] = None
    per_page: Optional[int] = 30
    page: Optional[int] = 1

# Branch schemas
class ListBranches(BaseModel):
    owner: str
    repo: str
    protected: Optional[bool] = None
    per_page: Optional[int] = 30
    page: Optional[int] = 1

class CreateBranch(BaseModel):
    owner: str
    repo: str
    branch: str
    from_branch: Optional[str] = "main"

# Tag schemas
class ListTags(BaseModel):
    owner: str
    repo: str
    per_page: Optional[int] = 30
    page: Optional[int] = 1

class GetTag(BaseModel):
    owner: str
    repo: str
    tag: str

# Pull request schemas
class GetPullRequest(BaseModel):
    owner: str
    repo: str
    pull_number: int

class UpdatePullRequest(BaseModel):
    owner: str
    repo: str
    pull_number: int
    title: Optional[str] = None
    body: Optional[str] = None
    state: Optional[str] = None
    base: Optional[str] = None

class ListPullRequests(BaseModel):
    owner: str
    repo: str
    state: Optional[str] = "open"
    head: Optional[str] = None
    base: Optional[str] = None
    sort: Optional[str] = "created"
    direction: Optional[str] = "desc"
    per_page: Optional[int] = 30
    page: Optional[int] = 1

class MergePullRequest(BaseModel):
    owner: str
    repo: str
    pull_number: int
    commit_title: Optional[str] = None
    commit_message: Optional[str] = None
    merge_method: Optional[str] = "merge"

class GetPullRequestFiles(BaseModel):
    owner: str
    repo: str
    pull_number: int
    per_page: Optional[int] = 30
    page: Optional[int] = 1

class GetPullRequestStatus(BaseModel):
    owner: str
    repo: str
    pull_number: int

class UpdatePullRequestBranch(BaseModel):
    owner: str
    repo: str
    pull_number: int

class GetPullRequestComments(BaseModel):
    owner: str
    repo: str
    pull_number: int
    per_page: Optional[int] = 30
    page: Optional[int] = 1

class CreatePullRequest(BaseModel):
    owner: str
    repo: str
    title: str
    head: str
    base: str
    body: Optional[str] = None
    draft: Optional[bool] = False
