from enum import Enum


class Tools(str, Enum):
    # Repository tools
    SEARCH_REPOSITORIES = "search_repositories"
    GET_REPOSITORY = "get_repository"
    CREATE_REPOSITORY = "create_repository"
    FORK_REPOSITORY = "fork_repository"
    
    # Code search tools
    SEARCH_CODE = "search_code"
    GET_FILE_CONTENTS = "get_file_contents"
    CREATE_OR_UPDATE_FILE = "create_or_update_file"
    PUSH_FILES = "push_files"
    
    # User tools
    SEARCH_USERS = "search_users"
    
    # Issue tools
    GET_ISSUE = "get_issue"
    ADD_ISSUE_COMMENT = "add_issue_comment"
    SEARCH_ISSUES = "search_issues"
    CREATE_ISSUE = "create_issue"
    LIST_ISSUES = "list_issues"
    UPDATE_ISSUE = "update_issue"
    GET_ISSUE_COMMENTS = "get_issue_comments"
    
    # Commit tools
    GET_COMMIT = "get_commit"
    LIST_COMMITS = "list_commits"
    
    # Branch tools
    LIST_BRANCHES = "list_branches"
    CREATE_BRANCH = "create_branch"
    
    # Tag tools
    LIST_TAGS = "list_tags"
    GET_TAG = "get_tag"
    
    # Pull request tools
    GET_PULL_REQUEST = "get_pull_request"
    UPDATE_PULL_REQUEST = "update_pull_request"
    LIST_PULL_REQUESTS = "list_pull_requests"
    MERGE_PULL_REQUEST = "merge_pull_request"
    GET_PULL_REQUEST_FILES = "get_pull_request_files"
    GET_PULL_REQUEST_STATUS = "get_pull_request_status"
    UPDATE_PULL_REQUEST_BRANCH = "update_pull_request_branch"
    GET_PULL_REQUEST_COMMENTS = "get_pull_request_comments"
    CREATE_PULL_REQUEST = "create_pull_request"
