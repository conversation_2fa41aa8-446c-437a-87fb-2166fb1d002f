from mcp.server.streamable_http_manager import StreamableHTTPSessionManager
from starlette.middleware.cors import CORSMiddleware
from constants.schema import *
from starlette.applications import Starlette
from event_store import InMemoryEventStore
from starlette.middleware import Middleware
from constants.enum import Tools
from helper.config import HOST, PORT
from helper.logger import logging
from starlette.routing import Route
from services.github_provider import *
from mcp.server import Server
import mcp.types as types
import contextlib
import uvicorn
import asyncio
import json
from contextvars import ContextVar

server = Server("github-mcp")

# Context variables to store request headers
github_token_context: ContextVar[str] = ContextVar('github_token')

def check_and_raise_on_error(result: dict) -> dict:
    """
    Check if the Google Sheets operation failed and raise an exception if it did.
    This ensures the MCP framework sets isError: true for failed operations.
    """
    if not result.get("success", True):
        # Extract error message from the result
        error_msg = result.get("error", "Unknown error occurred")
        raise Exception(error_msg)
    return result


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    List available tools.
    Each tool specifies its arguments using JSON Schema validation.
    """
    return [
        # Repository tools
        types.Tool(
            name=Tools.SEARCH_REPOSITORIES,
            description="Search for GitHub repositories. Returns a concise list with essential information. Use 'get_repository' for detailed information about a specific repository.",
            inputSchema=SearchRepositories.model_json_schema(),
        ),
        types.Tool(
            name=Tools.GET_REPOSITORY,
            description="Get detailed information about a GitHub repository including README and file structure",
            inputSchema=GetRepository.model_json_schema(),
        ),
        types.Tool(
            name=Tools.CREATE_REPOSITORY,
            description="Create a new GitHub repository in your account",
            inputSchema=CreateRepository.model_json_schema(),
        ),
        types.Tool(
            name=Tools.FORK_REPOSITORY,
            description="Fork a GitHub repository to your account or specified organization",
            inputSchema=ForkRepository.model_json_schema(),
        ),
        
        # Code search tools
        types.Tool(
            name=Tools.SEARCH_CODE,
            description="Search for code across GitHub repositories. Returns a concise list with file paths and repositories. Use 'get_file_contents' for full file content.",
            inputSchema=SearchCode.model_json_schema(),
        ),
        types.Tool(
            name=Tools.GET_FILE_CONTENTS,
            description="Get the contents of a file from a GitHub repository",
            inputSchema=GetFileContents.model_json_schema(),
        ),
        types.Tool(
            name=Tools.CREATE_OR_UPDATE_FILE,
            description="Create or update a single file in a GitHub repository. If updating an existing file, you must provide the current SHA of the file (the full 40-character SHA, not a shortened version).",
            inputSchema=CreateOrUpdateFile.model_json_schema(),
        ),
        types.Tool(
            name=Tools.PUSH_FILES,
            description="Push multiple files to a GitHub repository in a single commit",
            inputSchema=PushFiles.model_json_schema(),
        ),
        
        # User tools
        types.Tool(
            name=Tools.SEARCH_USERS,
            description="Search for GitHub users",
            inputSchema=SearchUsers.model_json_schema(),
        ),
        \
        # Issue tools
        types.Tool(
            name=Tools.GET_ISSUE,
            description="Get details of a specific issue in a GitHub repository",
            inputSchema=GetIssue.model_json_schema(),
        ),
        types.Tool(
            name=Tools.ADD_ISSUE_COMMENT,
            description="Add a comment to a specific issue in a GitHub repository",
            inputSchema=AddIssueComment.model_json_schema(),
        ),
        types.Tool(
            name=Tools.SEARCH_ISSUES,
            description="Search for issues in GitHub repositories",
            inputSchema=SearchIssues.model_json_schema(),
        ),
        types.Tool(
            name=Tools.CREATE_ISSUE,
            description="Create a new issue in a GitHub repository",
            inputSchema=CreateIssue.model_json_schema(),
        ),
        types.Tool(
            name=Tools.LIST_ISSUES,
            description="List issues in a GitHub repository",
            inputSchema=ListIssues.model_json_schema(),
        ),
        types.Tool(
            name=Tools.UPDATE_ISSUE,
            description="Update an existing issue in a GitHub repository",
            inputSchema=UpdateIssue.model_json_schema(),
        ),
        types.Tool(
            name=Tools.GET_ISSUE_COMMENTS,
            description="Get comments for a specific issue in a GitHub repository",
            inputSchema=GetIssueComments.model_json_schema(),
        ),
        
        # Commit tools
        types.Tool(
            name=Tools.GET_COMMIT,
            description="Get details for a commit from a GitHub repository",
            inputSchema=GetCommit.model_json_schema(),
        ),
        types.Tool(
            name=Tools.LIST_COMMITS,
            description="Get list of commits of a branch in a GitHub repository",
            inputSchema=ListCommits.model_json_schema(),
        ),
        
        # Branch tools
        types.Tool(
            name=Tools.LIST_BRANCHES,
            description="List branches in a GitHub repository",
            inputSchema=ListBranches.model_json_schema(),
        ),
        types.Tool(
            name=Tools.CREATE_BRANCH,
            description="Create a new branch in a GitHub repository",
            inputSchema=CreateBranch.model_json_schema(),
        ),
        
        # Tag tools
        types.Tool(
            name=Tools.LIST_TAGS,
            description="List git tags in a GitHub repository",
            inputSchema=ListTags.model_json_schema(),
        ),
        types.Tool(
            name=Tools.GET_TAG,
            description="Get details about a specific git tag in a GitHub repository",
            inputSchema=GetTag.model_json_schema(),
        ),
        
        # Pull request tools
        types.Tool(
            name=Tools.GET_PULL_REQUEST,
            description="Get details of a specific pull request in a GitHub repository",
            inputSchema=GetPullRequest.model_json_schema(),
        ),
        types.Tool(
            name=Tools.UPDATE_PULL_REQUEST,
            description="Update an existing pull request in a GitHub repository",
            inputSchema=UpdatePullRequest.model_json_schema(),
        ),
        types.Tool(
            name=Tools.LIST_PULL_REQUESTS,
            description="List pull requests in a GitHub repository",
            inputSchema=ListPullRequests.model_json_schema(),
        ),
        types.Tool(
            name=Tools.MERGE_PULL_REQUEST,
            description="Merge a pull request in a GitHub repository",
            inputSchema=MergePullRequest.model_json_schema(),
        ),
        types.Tool(
            name=Tools.GET_PULL_REQUEST_FILES,
            description="Get the files changed in a specific pull request",
            inputSchema=GetPullRequestFiles.model_json_schema(),
        ),
        types.Tool(
            name=Tools.GET_PULL_REQUEST_STATUS,
            description="Get the status of a specific pull request",
            inputSchema=GetPullRequestStatus.model_json_schema(),
        ),
        types.Tool(
            name=Tools.UPDATE_PULL_REQUEST_BRANCH,
            description="Update the branch of a pull request with the latest changes from the base branch (not implemented)",
            inputSchema=UpdatePullRequestBranch.model_json_schema(),
        ),
        types.Tool(
            name=Tools.GET_PULL_REQUEST_COMMENTS,
            description="Get comments for a specific pull request",
            inputSchema=GetPullRequestComments.model_json_schema(),
        ),
        types.Tool(
            name=Tools.CREATE_PULL_REQUEST,
            description="Create a new pull request in a GitHub repository",
            inputSchema=CreatePullRequest.model_json_schema(),
        ),
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict | None) -> types.CallToolResult:
    """
    Handle tool execution requests.
    Tools can modify server state and notify clients of changes.
    """
    try:
        # Get GitHub token from context
        try:
            github_token = github_token_context.get()
            # Check if github token are available
            if not github_token:
                error_msg = {
                    "error": {
                        "code": "AUTHENTICATION_REQUIRED",
                        "message": "Authentication required to access this resource",
                        "details": {
                            "authentication_requirements": [
                                {
                                    "provider": "github",
                                    "auth_type": "bearer",
                                    "header_name": "Authorization",
                                    "header_format": "Bearer {access_token}",
                                    "required_scopes": ["repo", "user:email", "read:org"], # optional
                                    "token_source": "access_token"
                                },
                            ],
                        }
                    }
                }
                raise Exception(error_msg)
        except LookupError:
            error_msg = {
                    "error": {
                        "code": "AUTHENTICATION_REQUIRED",
                        "message": "Authentication required to access this resource",
                        "details": {
                            "authentication_requirements": [
                                {
                                    "provider": "github",
                                    "auth_type": "bearer",
                                    "header_name": "Authorization",
                                    "header_format": "Bearer {access_token}",
                                    "required_scopes": ["repo", "user:email", "read:org"], # optional
                                    "token_source": "access_token"
                                },
                            ],
                        }
                    }
                }
            raise Exception(error_msg)

        match name:
            # Repository tools
            case Tools.SEARCH_REPOSITORIES:
                result = search_repositories(
                    access_token=github_token,
                    query=arguments.get("query"),
                    sort=arguments.get("sort"),
                    order=arguments.get("order"),
                    per_page=arguments.get("per_page", 30),
                    page=arguments.get("page", 1)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.GET_REPOSITORY:
                result = get_repository(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.CREATE_REPOSITORY:
                result = create_repository(
                    access_token=github_token,
                    name=arguments.get("name"),
                    description=arguments.get("description"),
                    private=arguments.get("private", False),
                    auto_init=arguments.get("auto_init", False)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.FORK_REPOSITORY:
                result = fork_repository(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    organization=arguments.get("organization")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            # Code search tools
            case Tools.SEARCH_CODE:
                result = search_code(
                    access_token=github_token,
                    query=arguments.get("query"),
                    sort=arguments.get("sort"),
                    order=arguments.get("order"),
                    per_page=arguments.get("per_page", 30),
                    page=arguments.get("page", 1)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.GET_FILE_CONTENTS:
                result = get_file_contents(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    path=arguments.get("path"),
                    ref=arguments.get("ref")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.CREATE_OR_UPDATE_FILE:
                result = create_or_update_file(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    path=arguments.get("path"),
                    message=arguments.get("message"),
                    content=arguments.get("content"),
                    sha=arguments.get("sha"),
                    branch=arguments.get("branch")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.PUSH_FILES:
                result = push_files(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    branch=arguments.get("branch"),
                    message=arguments.get("message"),
                    files=arguments.get("files", [])
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            # User tools
            case Tools.SEARCH_USERS:
                result = search_users(
                    access_token=github_token,
                    query=arguments.get("query"),
                    sort=arguments.get("sort"),
                    order=arguments.get("order"),
                    per_page=arguments.get("per_page", 30),
                    page=arguments.get("page", 1)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            # Issue tools
            case Tools.GET_ISSUE:
                result = get_issue(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    issue_number=arguments.get("issue_number")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.ADD_ISSUE_COMMENT:
                result = add_issue_comment(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    issue_number=arguments.get("issue_number"),
                    body=arguments.get("body")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.SEARCH_ISSUES:
                result = search_issues(
                    access_token=github_token,
                    query=arguments.get("query"),
                    sort=arguments.get("sort"),
                    order=arguments.get("order"),
                    per_page=arguments.get("per_page", 30),
                    page=arguments.get("page", 1)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.CREATE_ISSUE:
                result = create_issue(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    title=arguments.get("title"),
                    body=arguments.get("body"),
                    assignees=arguments.get("assignees"),
                    labels=arguments.get("labels")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.LIST_ISSUES:
                result = list_issues(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    state=arguments.get("state", "open"),
                    sort=arguments.get("sort", "created"),
                    direction=arguments.get("direction", "desc"),
                    per_page=arguments.get("per_page", 30),
                    page=arguments.get("page", 1)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.UPDATE_ISSUE:
                result = update_issue(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    issue_number=arguments.get("issue_number"),
                    title=arguments.get("title"),
                    body=arguments.get("body"),
                    state=arguments.get("state"),
                    assignees=arguments.get("assignees"),
                    labels=arguments.get("labels")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.GET_ISSUE_COMMENTS:
                result = get_issue_comments(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    issue_number=arguments.get("issue_number"),
                    per_page=arguments.get("per_page", 30),
                    page=arguments.get("page", 1)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            # Commit tools
            case Tools.GET_COMMIT:
                result = get_commit(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    ref=arguments.get("ref")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.LIST_COMMITS:
                result = list_commits(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    sha=arguments.get("sha"),
                    path=arguments.get("path"),
                    author=arguments.get("author"),
                    since=arguments.get("since"),
                    until=arguments.get("until"),
                    per_page=arguments.get("per_page", 30),
                    page=arguments.get("page", 1)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            # Branch tools
            case Tools.LIST_BRANCHES:
                result = list_branches(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    protected=arguments.get("protected"),
                    per_page=arguments.get("per_page", 30),
                    page=arguments.get("page", 1)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.CREATE_BRANCH:
                result = create_branch(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    branch=arguments.get("branch"),
                    from_branch=arguments.get("from_branch", "main")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            # Tag tools
            case Tools.LIST_TAGS:
                result = list_tags(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    per_page=arguments.get("per_page", 30),
                    page=arguments.get("page", 1)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.GET_TAG:
                result = get_tag(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    tag=arguments.get("tag")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            # Pull request tools
            case Tools.GET_PULL_REQUEST:
                result = get_pull_request(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    pull_number=arguments.get("pull_number")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.UPDATE_PULL_REQUEST:
                result = update_pull_request(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    pull_number=arguments.get("pull_number"),
                    title=arguments.get("title"),
                    body=arguments.get("body"),
                    state=arguments.get("state"),
                    base=arguments.get("base")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.LIST_PULL_REQUESTS:
                result = list_pull_requests(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    state=arguments.get("state", "open"),
                    head=arguments.get("head"),
                    base=arguments.get("base"),
                    sort=arguments.get("sort", "created"),
                    direction=arguments.get("direction", "desc"),
                    per_page=arguments.get("per_page", 30),
                    page=arguments.get("page", 1)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.MERGE_PULL_REQUEST:
                result = merge_pull_request(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    pull_number=arguments.get("pull_number"),
                    commit_title=arguments.get("commit_title"),
                    commit_message=arguments.get("commit_message"),
                    merge_method=arguments.get("merge_method", "merge")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.GET_PULL_REQUEST_FILES:
                result = get_pull_request_files(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    pull_number=arguments.get("pull_number"),
                    per_page=arguments.get("per_page", 30),
                    page=arguments.get("page", 1)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.GET_PULL_REQUEST_STATUS:
                result = get_pull_request_status(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    pull_number=arguments.get("pull_number")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.UPDATE_PULL_REQUEST_BRANCH:
                result = update_pull_request_branch(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    pull_number=arguments.get("pull_number")
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.GET_PULL_REQUEST_COMMENTS:
                result = get_pull_request_comments(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    pull_number=arguments.get("pull_number"),
                    per_page=arguments.get("per_page", 30),
                    page=arguments.get("page", 1)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case Tools.CREATE_PULL_REQUEST:
                result = create_pull_request(
                    access_token=github_token,
                    owner=arguments.get("owner"),
                    repo=arguments.get("repo"),
                    title=arguments.get("title"),
                    head=arguments.get("head"),
                    base=arguments.get("base"),
                    body=arguments.get("body"),
                    draft=arguments.get("draft", False)
                )
                result = check_and_raise_on_error(result)
                return [types.TextContent(type="text", text=json.dumps(result, indent=2))]
            
            case _:
                raise Exception('Invalid function tools')

    except Exception as error:
        print("Error:", error)
        # Let the MCP framework handle the error response with isError: true
        raise error


async def create_app():

    # Create an event store for resumability
    event_store = InMemoryEventStore(max_events_per_stream=100)

    # Create the session manager with the event store
    try:
        # Try with auth parameters (newer MCP versions)
        session_manager = StreamableHTTPSessionManager(
            app=server,
            event_store=event_store,  # Use our event store for resumability
            json_response=False,  # Use SSE format for responses
            stateless=False,  # Stateful mode for better user experience
        )
        logging.info(
            "StreamableHTTPSessionManager initialized with authentication support"
        )
    except TypeError:
        # Fallback for older MCP versions that don't support auth
        logging.warning(
            "Your MCP version doesn't support authentication in StreamableHTTPSessionManager"
        )
        logging.warning(
            "Initializing StreamableHTTPSessionManager without authentication"
        )

        # Try with just the basic parameters
        try:
            session_manager = StreamableHTTPSessionManager(
                app=server,
                event_store=event_store,
                json_response=False,
            )
            logging.info(
                "StreamableHTTPSessionManager initialized without authentication"
            )
        except TypeError:
            # If that still fails, try with minimal parameters
            logging.warning(
                "Falling back to minimal StreamableHTTPSessionManager initialization"
            )
            session_manager = StreamableHTTPSessionManager(app=server)
    except Exception as e:
        logging.error(f"Failed to initialize StreamableHTTPSessionManager: {e}")
        session_manager = None


    # Create a class for handling streamable HTTP connections
    class HandleStreamableHttp:
        def __init__(self, session_manager):
            self.session_manager = session_manager

        def _extract_headers(self, scope):
            """Extract authorization headers from request scope."""
            headers = dict(scope.get('headers', []))
            
            # Extract Authorization header (GitHub Personal Access Token)
            github_token = None
            auth_header = headers.get(b'authorization')
            if auth_header:
                github_token = auth_header.decode('utf-8')
                github_token = github_token.replace('Bearer ', '')
            return github_token

        async def __call__(self, scope, receive, send):
            if self.session_manager is not None:
                try:
                    logging.info("Handling Streamable HTTP connection ....")
                    
                    # Extract headers
                    github_token = self._extract_headers(scope)
                    
                    # Set context variable for GitHub token
                    if github_token:
                        github_token = github_token.replace('Bearer ', '')
                        github_token_context.set(github_token)
                    
                    await self.session_manager.handle_request(scope, receive, send)
                    logging.info("Streamable HTTP connection closed ....")
                except Exception as e:
                    logging.error(f"Error handling Streamable HTTP request: {e}")
                    await send({
                        "type": "http.response.start",
                        "status": 500,
                        "headers": [(b"content-type", b"application/json")],
                    })
                    await send({
                        "type": "http.response.body",
                        "body": json.dumps({
                            "error": f"Internal server error: {str(e)}"
                        }).encode("utf-8"),
                    })
            else:
                # Return a 501 Not Implemented response if streamable HTTP is not available
                await send(
                    {
                        "type": "http.response.start",
                        "status": 501,
                        "headers": [(b"content-type", b"application/json")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": json.dumps(
                            {"error": "Streamable HTTP transport is not available"}
                        ).encode("utf-8"),
                    }
                )

    
    # Helper functions for OAuth handlers
    async def get_request_body(receive):
        """Get request body from ASGI receive function."""
        body = b""
        more_body = True

        while more_body:
            message = await receive()
            body += message.get("body", b"")
            more_body = message.get("more_body", False)

        return body.decode("utf-8")

    async def send_json_response(send, status, data):
        """Send JSON response."""
        await send(
            {
                "type": "http.response.start",
                "status": status,
                "headers": [(b"content-type", b"application/json")],
            }
        )
        await send(
            {
                "type": "http.response.body",
                "body": json.dumps(data).encode("utf-8"),
            }
        )

    # Define routes
    routes = []

    # Add Streamable HTTP route if available
    if session_manager is not None:
        routes.append(
            Route(
                "/mcp", endpoint=HandleStreamableHttp(session_manager), methods=["POST"]
            )
        )

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        )
    ]

    # Define lifespan for session manager
    @contextlib.asynccontextmanager
    async def lifespan(app):
        """Context manager for session manager."""
        if session_manager is not None:
            async with session_manager.run():
                logging.info("Application started with StreamableHTTP session manager!")
                try:
                    yield
                finally:
                    logging.info("Application shutting down...")
        else:
            # No session manager, just yield
            yield

    return Starlette(routes=routes, middleware=middleware, lifespan=lifespan)


async def start_server():
    """Start the server asynchronously."""
    app = await create_app()
    logging.info(f"Starting server at {HOST}:{PORT}")

    # Use uvicorn's async API
    config = uvicorn.Config(app, host=HOST, port=PORT)
    server = uvicorn.Server(config)
    await server.serve()



if __name__ == "__main__":
    while True:
        try:
            # Use asyncio.run to run the async start_server function
            asyncio.run(start_server())
        except KeyboardInterrupt:
            logging.info("Server stopped by user")
            break
        except Exception as e:
            logging.error(f"Server crashed with error: {e}")
            continue
