# GitHub MCP Server

A Model Context Protocol (MCP) server that provides comprehensive GitHub API integration with 31 tools for managing repositories, issues, pull requests, commits, branches, tags, and more.

## Features

This MCP server provides 31 GitHub API tools organized into the following categories:

### Repository Management
- **search_repositories**: Search for GitHub repositories
- **get_repository**: Get detailed repository information
- **create_repository**: Create new repositories
- **fork_repository**: Fork repositories to your account

### Code & File Management
- **search_code**: Search for code across repositories
- **get_file_contents**: Get file contents from repositories
- **create_or_update_file**: Create or update single files
- **push_files**: Push multiple files in a single commit

### User Management
- **search_users**: Search for GitHub users

### Issue Management
- **get_issue**: Get issue details
- **add_issue_comment**: Add comments to issues
- **search_issues**: Search for issues across repositories
- **create_issue**: Create new issues
- **list_issues**: List repository issues
- **update_issue**: Update existing issues
- **get_issue_comments**: Get issue comments

### Commit Management
- **get_commit**: Get commit details
- **list_commits**: List repository commits

### Branch Management
- **list_branches**: List repository branches
- **create_branch**: Create new branches

### Tag Management
- **list_tags**: List repository tags
- **get_tag**: Get tag details

### Pull Request Management
- **get_pull_request**: Get pull request details
- **update_pull_request**: Update pull requests
- **list_pull_requests**: List repository pull requests
- **merge_pull_request**: Merge pull requests
- **get_pull_request_files**: Get pull request file changes
- **get_pull_request_status**: Get pull request status
- **update_pull_request_branch**: Update PR branch (not implemented)
- **get_pull_request_comments**: Get pull request comments
- **create_pull_request**: Create new pull requests

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd github-mcp
```

2. Install dependencies using uv (recommended) or pip:
```bash
# Using uv
uv pip install -e .

# Or using pip
pip install -e .
```

## Configuration

### GitHub Personal Access Token

You need a GitHub Personal Access Token to use this server. Create one at:
https://github.com/settings/tokens

The token should have appropriate scopes based on what operations you plan to perform:
- `repo` - Full control of private repositories
- `public_repo` - Access to public repositories
- `user` - Read/write access to profile info
- `admin:org` - Full control of orgs and teams (if working with organizations)

### Environment Variables

Create a `.env` file (optional):
```env
HOST=0.0.0.0
PORT=5001
LOG_LEVEL=INFO
```

## Usage

### Running the Server

Start the MCP server:
```bash
python -m src.server
```

The server will start on `http://localhost:5001` by default.

### Authentication

All requests must include your GitHub Personal Access Token in the Authorization header:
```
Authorization: Bearer your_github_token_here
```

Or:
```
Authorization: your_github_token_here
```

### Example Tool Calls

#### Search Repositories
```json
{
  "tool": "search_repositories",
  "arguments": {
    "query": "language:python stars:>1000",
    "sort": "stars",
    "order": "desc",
    "per_page": 10
  }
}
```

#### Get Repository Information
```json
{
  "tool": "get_repository",
  "arguments": {
    "owner": "octocat",
    "repo": "Hello-World"
  }
}
```

#### Create an Issue
```json
{
  "tool": "create_issue",
  "arguments": {
    "owner": "your-username",
    "repo": "your-repo",
    "title": "Bug report",
    "body": "Description of the bug",
    "labels": ["bug", "priority-high"]
  }
}
```

#### Create a Pull Request
```json
{
  "tool": "create_pull_request",
  "arguments": {
    "owner": "your-username",
    "repo": "your-repo",
    "title": "Add new feature",
    "head": "feature-branch",
    "base": "main",
    "body": "Description of changes"
  }
}
```

## API Reference

All tools follow GitHub's REST API v4 conventions. Parameters and responses match the official GitHub API documentation.

### Common Parameters

Most tools support pagination:
- `per_page`: Number of results per page (default: 30, max: 100)
- `page`: Page number (default: 1)

Search tools support:
- `sort`: Sort field
- `order`: Sort order (`asc` or `desc`)

## Error Handling

The server returns structured error responses:
```json
{
  "success": false,
  "error": "Error description",
  "status_code": 404
}
```

Common error scenarios:
- **401 Unauthorized**: Invalid or missing GitHub token
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Repository, issue, or resource not found
- **422 Unprocessable Entity**: Invalid request parameters

## Development

### Project Structure
```
github-mcp/
├── src/
│   ├── __init__.py
│   ├── server.py              # Main MCP server
│   ├── event_store.py         # Event storage for resumability
│   ├── constants/
│   │   ├── enum.py           # Tool name enums
│   │   └── schema.py         # Pydantic schemas
│   ├── helper/
│   │   ├── config.py         # Configuration
│   │   └── logger.py         # Logging setup
│   └── services/
│       └── github_provider.py # GitHub API client
├── pyproject.toml
├── README.md
└── .env.example
```

### Adding New Tools

1. Add the tool name to `src/constants/enum.py`
2. Create a Pydantic schema in `src/constants/schema.py`
3. Implement the method in `src/services/github_provider.py`
4. Add the tool definition and handler in `src/server.py`

## Requirements

- Python >= 3.13
- MCP >= 1.9.4
- requests >= 2.31.0
- starlette >= 0.37.2
- uvicorn >= 0.25.0

## License

This project is licensed under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions:
- Create an issue on GitHub
- Check the GitHub API documentation: https://docs.github.com/en/rest

## Changelog

### v0.1.0
- Initial release with 31 GitHub API tools
- Support for repositories, issues, pull requests, commits, branches, and tags
- Full GitHub REST API v4 integration
- MCP server implementation with resumability support
