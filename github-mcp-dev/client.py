#!/usr/bin/env python3
"""
Comprehensive test client for GitHub MCP Server.
Tests all available tools with proper GitHub authentication.
"""

import asyncio
import json
import os
from mcp.client.streamable_http import streamablehttp_client
from mcp.client.session import ClientSession

# Test configuration
TEST_OWNER = "abhishekrapid"  # Default GitHub test user
TEST_REPO = "fast-api"  # Default GitHub test repo
TEST_SEARCH_QUERY = "fast"  # Default search query
TEST_USERNAME = "abhishek"  # Default user to search for
TEST_ISSUE_NUMBER = 1  # Default issue number for testing
TEST_PULL_NUMBER = 1  # Default pull request number for testing
TEST_COMMIT_SHA = "main"  # Default commit reference
TEST_BRANCH_NAME = "main"  # Default branch name
TEST_TAG_NAME = "v1.0.0"  # Default tag name for testing

# Global variables to store test results
test_results = {}
created_repo_name = None
created_issue_number = None
created_pull_number = None

async def get_github_token():
    """
    Get GitHub Personal Access Token from environment variables.
    """
    github_token = 'XXXXXX'
    
    if not github_token:
        print("❌ Missing GITHUB_TOKEN environment variable")
        print("   Please set your GitHub Personal Access Token:")
        print("   export GITHUB_TOKEN='your_github_token_here'")
        print("   You can create one at: https://github.com/settings/tokens")
        return None
    
    return github_token

async def test_server_connectivity(session):
    """Test basic server connectivity and list available tools."""
    print("\n" + "="*50)
    print("🔧 Testing SERVER_CONNECTIVITY")
    print("="*50)
    
    try:
        # Get list of available tools
        tools = await session.list_tools()
        available_tools = [tool.name for tool in tools.tools]
        expected_tools = [
            "search_repositories", "get_repository", "create_repository", "fork_repository",
            "search_code", "get_file_contents", "create_or_update_file", "push_files",
            "search_users", "get_issue", "add_issue_comment", "search_issues", 
            "create_issue", "list_issues", "update_issue", "get_issue_comments",
            "get_commit", "list_commits", "list_branches", "create_branch",
            "list_tags", "get_tag", "get_pull_request", "update_pull_request",
            "list_pull_requests", "merge_pull_request", "get_pull_request_files",
            "get_pull_request_status", "update_pull_request_branch", 
            "get_pull_request_comments", "create_pull_request"
        ]
        
        print(f"✅ Server connectivity successful")
        print(f"📚 Available tools ({len(available_tools)}): {', '.join(available_tools)}")
        
        # Check if all expected tools are available
        missing_tools = [tool for tool in expected_tools if tool not in available_tools]
        if missing_tools:
            print(f"⚠️  Missing expected tools: {missing_tools}")
            return False
        else:
            print(f"✅ All expected tools are available")
            return True
            
    except Exception as e:
        print(f"❌ Server connectivity failed: {e}")
        return False

# Repository Tools
async def test_search_repositories(session):
    """Test the search_repositories tool."""
    print("\n" + "="*50)
    print("🔍 Testing SEARCH_REPOSITORIES")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "search_repositories",
            arguments={
                "query": TEST_SEARCH_QUERY,
                "sort": "stars",
                "order": "desc",
                "per_page": 3
            }
        )
        print("✅ search_repositories successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ search_repositories failed: {e}")
        return False

async def test_get_repository(session):
    """Test the get_repository tool."""
    print("\n" + "="*50)
    print("📁 Testing GET_REPOSITORY")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "get_repository",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO
            }
        )
        print("✅ get_repository successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ get_repository failed: {e}")
        return False

async def test_fork_repository(session):
    """Test the fork_repository tool."""
    print("\n" + "="*50)
    print("🍴 Testing FORK_REPOSITORY")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "fork_repository",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO
            }
        )
        print("✅ fork_repository successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ fork_repository failed: {e}")
        return False

# Code Search Tools
async def test_search_code(session):
    """Test the search_code tool."""
    print("\n" + "="*50)
    print("🔍 Testing SEARCH_CODE")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "search_code",
            arguments={
                "query": "function",
                "sort": "indexed",
                "per_page": 3
            }
        )
        print("✅ search_code successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ search_code failed: {e}")
        return False

async def test_get_file_contents(session):
    """Test the get_file_contents tool."""
    print("\n" + "="*50)
    print("📄 Testing GET_FILE_CONTENTS")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "get_file_contents",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "path": "README"
            }
        )
        print("✅ get_file_contents successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ get_file_contents failed: {e}")
        return False

# User Tools
async def test_search_users(session):
    """Test the search_users tool."""
    print("\n" + "="*50)
    print("👤 Testing SEARCH_USERS")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "search_users",
            arguments={
                "query": TEST_USERNAME,
                "sort": "followers",
                "per_page": 3
            }
        )
        print("✅ search_users successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ search_users failed: {e}")
        return False

# Issue Tools
async def test_get_issue(session):
    """Test the get_issue tool."""
    print("\n" + "="*50)
    print("🐛 Testing GET_ISSUE")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "get_issue",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "issue_number": TEST_ISSUE_NUMBER
            }
        )
        print("✅ get_issue successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ get_issue failed: {e}")
        return False

async def test_search_issues(session):
    """Test the search_issues tool."""
    print("\n" + "="*50)
    print("🔍 Testing SEARCH_ISSUES")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "search_issues",
            arguments={
                "query": "is:issue is:open",
                "sort": "created",
                "per_page": 3
            }
        )
        print("✅ search_issues successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ search_issues failed: {e}")
        return False

async def test_list_issues(session):
    """Test the list_issues tool."""
    print("\n" + "="*50)
    print("📋 Testing LIST_ISSUES")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "list_issues",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "state": "open",
                "per_page": 3
            }
        )
        print("✅ list_issues successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ list_issues failed: {e}")
        return False

async def test_get_issue_comments(session):
    """Test the get_issue_comments tool."""
    print("\n" + "="*50)
    print("💬 Testing GET_ISSUE_COMMENTS")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "get_issue_comments",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "issue_number": TEST_ISSUE_NUMBER,
                "per_page": 3
            }
        )
        print("✅ get_issue_comments successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ get_issue_comments failed: {e}")
        return False

# Commit Tools
async def test_get_commit(session):
    """Test the get_commit tool."""
    print("\n" + "="*50)
    print("📝 Testing GET_COMMIT")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "get_commit",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "ref": TEST_COMMIT_SHA
            }
        )
        print("✅ get_commit successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ get_commit failed: {e}")
        return False

async def test_list_commits(session):
    """Test the list_commits tool."""
    print("\n" + "="*50)
    print("📚 Testing LIST_COMMITS")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "list_commits",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "per_page": 3
            }
        )
        print("✅ list_commits successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ list_commits failed: {e}")
        return False

# Branch Tools
async def test_list_branches(session):
    """Test the list_branches tool."""
    print("\n" + "="*50)
    print("🌿 Testing LIST_BRANCHES")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "list_branches",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "per_page": 3
            }
        )
        print("✅ list_branches successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ list_branches failed: {e}")
        return False

# Tag Tools
async def test_list_tags(session):
    """Test the list_tags tool."""
    print("\n" + "="*50)
    print("🏷️  Testing LIST_TAGS")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "list_tags",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "per_page": 3
            }
        )
        print("✅ list_tags successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ list_tags failed: {e}")
        return False

async def test_get_tag(session):
    """Test the get_tag tool."""
    print("\n" + "="*50)
    print("🏷️  Testing GET_TAG")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "get_tag",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "tag": TEST_TAG_NAME
            }
        )
        print("✅ get_tag successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ get_tag failed: {e}")
        return False

# Pull Request Tools
async def test_get_pull_request(session):
    """Test the get_pull_request tool."""
    print("\n" + "="*50)
    print("🔀 Testing GET_PULL_REQUEST")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "get_pull_request",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "pull_number": TEST_PULL_NUMBER
            }
        )
        print("✅ get_pull_request successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ get_pull_request failed: {e}")
        return False

async def test_list_pull_requests(session):
    """Test the list_pull_requests tool."""
    print("\n" + "="*50)
    print("📋 Testing LIST_PULL_REQUESTS")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "list_pull_requests",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "state": "open",
                "per_page": 3
            }
        )
        print("✅ list_pull_requests successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ list_pull_requests failed: {e}")
        return False

async def test_get_pull_request_files(session):
    """Test the get_pull_request_files tool."""
    print("\n" + "="*50)
    print("📁 Testing GET_PULL_REQUEST_FILES")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "get_pull_request_files",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "pull_number": TEST_PULL_NUMBER,
                "per_page": 3
            }
        )
        print("✅ get_pull_request_files successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ get_pull_request_files failed: {e}")
        return False

async def test_get_pull_request_status(session):
    """Test the get_pull_request_status tool."""
    print("\n" + "="*50)
    print("✅ Testing GET_PULL_REQUEST_STATUS")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "get_pull_request_status",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "pull_number": TEST_PULL_NUMBER
            }
        )
        print("✅ get_pull_request_status successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ get_pull_request_status failed: {e}")
        return False

async def test_get_pull_request_comments(session):
    """Test the get_pull_request_comments tool."""
    print("\n" + "="*50)
    print("💬 Testing GET_PULL_REQUEST_COMMENTS")
    print("="*50)
    
    try:
        result = await session.call_tool(
            "get_pull_request_comments",
            arguments={
                "owner": TEST_OWNER,
                "repo": TEST_REPO,
                "pull_number": TEST_PULL_NUMBER,
                "per_page": 3
            }
        )
        print("✅ get_pull_request_comments successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ get_pull_request_comments failed: {e}")
        return False

async def test_all_tools(server_url: str = "http://localhost:5001"):
    """Test all available tools in the GitHub MCP server."""
    print("="*70)
    print("🚀 GITHUB MCP SERVER - COMPREHENSIVE TOOL TEST")
    print("="*70)
    
    # Get GitHub token
    github_token = await get_github_token()
    if not github_token:
        return
    
    # Set up headers with GitHub token
    headers = {
        "Authorization": f"Bearer {github_token}"
    }
    
    print(f"🔗 Connecting to server: {server_url}/mcp")
    print("🔐 Using GitHub Personal Access Token authentication")
    
    try:
        async with streamablehttp_client(f"{server_url}/mcp", headers=headers) as (read, write, _):
            async with ClientSession(read, write) as session:
                # Initialize session
                await session.initialize()
                print("✅ Connection initialized")
                
                # Get list of available tools
                tools = await session.list_tools()
                print(f"📚 Available tools ({len(tools.tools)}): {[tool.name for tool in tools.tools]}")
                
                # Test results tracking
                test_results = {}
                
                # Run all tests in logical order
                test_functions = [
                    ("server_connectivity", test_server_connectivity),
                    # Repository tools
                    # ("search_repositories", test_search_repositories),
                    # ("get_repository", test_get_repository),
                    # ("fork_repository", test_fork_repository),
                    # Code search tools
                    # ("search_code", test_search_code),
                    ("get_file_contents", test_get_file_contents),
                    # User tools
                    # ("search_users", test_search_users),
                    # Issue tools
                    ("get_issue", test_get_issue),
                    # ("search_issues", test_search_issues),
                    ("list_issues", test_list_issues),
                    ("get_issue_comments", test_get_issue_comments),
                    # Commit tools
                    # ("get_commit", test_get_commit),
                    # ("list_commits", test_list_commits),
                    # Branch tools
                    # ("list_branches", test_list_branches),
                    # Tag tools
                    # ("list_tags", test_list_tags),
                    ("get_tag", test_get_tag),
                    # Pull request tools
                    ("get_pull_request", test_get_pull_request),
                    ("list_pull_requests", test_list_pull_requests),
                    ("get_pull_request_files", test_get_pull_request_files),
                    ("get_pull_request_status", test_get_pull_request_status),
                    ("get_pull_request_comments", test_get_pull_request_comments),
                ]
                
                for test_name, test_func in test_functions:
                    try:
                        result = await test_func(session)
                        test_results[test_name] = result
                    except Exception as e:
                        print(f"❌ {test_name} failed with exception: {e}")
                        test_results[test_name] = False
                
                # Print summary
                print("\n" + "="*70)
                print("📊 TEST SUMMARY")
                print("="*70)
                
                passed_tests = sum(1 for result in test_results.values() if result)
                total_tests = len(test_results)
                
                for test_name, result in test_results.items():
                    status = "✅ PASS" if result else "❌ FAIL"
                    print(f"{status} {test_name}")
                
                print(f"\n🎯 Results: {passed_tests}/{total_tests} tests passed")
                
                if passed_tests == total_tests:
                    print("🎉 All tests passed! The GitHub MCP server is working correctly.")
                else:
                    print("⚠️  Some tests failed. Check the logs above for details.")
                
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure the MCP server is running on the specified port")
        print("2. Verify your GitHub Personal Access Token is valid and has required permissions")
        print("3. Check that your GITHUB_TOKEN environment variable is set correctly")
        print("4. Ensure the server is accessible at the specified URL")

async def test_specific_tool(tool_name: str, server_url: str = "http://localhost:5001"):
    """Test a specific tool by name."""
    print(f"🎯 Testing specific tool: {tool_name}")
    
    # Get GitHub token
    github_token = await get_github_token()
    if not github_token:
        return
    
    # Set up headers with GitHub token
    headers = {
        "Authorization": f"Bearer {github_token}"
    }
    
    async with streamablehttp_client(f"{server_url}/mcp", headers=headers) as (read, write, _):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            # Map tool names to test functions
            tool_map = {
                "search_repositories": test_search_repositories,
                "get_repository": test_get_repository,
                "fork_repository": test_fork_repository,
                "search_code": test_search_code,
                "get_file_contents": test_get_file_contents,
                "search_users": test_search_users,
                "get_issue": test_get_issue,
                "search_issues": test_search_issues,
                "list_issues": test_list_issues,
                "get_issue_comments": test_get_issue_comments,
                "get_commit": test_get_commit,
                "list_commits": test_list_commits,
                "list_branches": test_list_branches,
                "list_tags": test_list_tags,
                "get_tag": test_get_tag,
                # "get_pull_request": test_get_pull_request,
                "list_pull_requests": test_list_pull_requests,
                "get_pull_request_files": test_get_pull_request_files,
                "get_pull_request_status": test_get_pull_request_status,
                "get_pull_request_comments": test_get_pull_request_comments,
            }
            
            if tool_name in tool_map:
                await tool_map[tool_name](session)
            else:
                print(f"❌ Unknown tool: {tool_name}")
                print(f"Available tools: {list(tool_map.keys())}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # Check if first argument is a tool name or help
        if sys.argv[1] in ["help", "--help", "-h"]:
            print("GitHub MCP Server Test Client")
            print("============================")
            print("Usage:")
            print("  python client.py                    - Run all tests")
            print("  python client.py [server_url]       - Run all tests against specified server")
            print("  python client.py [tool] [server]    - Run specific tool test")
            print("")
            print("Available GitHub tools:")
            print("  Repository tools:")
            print("    search_repositories   - Search for GitHub repositories")
            print("    get_repository       - Get detailed repository information")
            print("    fork_repository      - Fork a repository")
            print("")
            print("  Code search tools:")
            print("    search_code          - Search for code across repositories")
            print("    get_file_contents    - Get file contents from a repository")
            print("")
            print("  User tools:")
            print("    search_users         - Search for GitHub users")
            print("")
            print("  Issue tools:")
            print("    get_issue           - Get details of a specific issue")
            print("    search_issues       - Search for issues")
            print("    list_issues         - List issues in a repository")
            print("    get_issue_comments  - Get comments for an issue")
            print("")
            print("  Commit tools:")
            print("    get_commit          - Get details of a specific commit")
            print("    list_commits        - List commits in a repository")
            print("")
            print("  Branch tools:")
            print("    list_branches       - List branches in a repository")
            print("")
            print("  Tag tools:")
            print("    list_tags           - List tags in a repository")
            print("    get_tag             - Get details of a specific tag")
            print("")
            print("  Pull request tools:")
            print("    get_pull_request         - Get details of a specific pull request")
            print("    list_pull_requests       - List pull requests in a repository")
            print("    get_pull_request_files   - Get files changed in a pull request")
            print("    get_pull_request_status  - Get status of a pull request")
            print("    get_pull_request_comments - Get comments for a pull request")
            print("")
            print("Environment variables required:")
            print("  GITHUB_TOKEN - Your GitHub Personal Access Token")
            print("                 Create one at: https://github.com/settings/tokens")
        elif sys.argv[1] in [
            "search_repositories", "get_repository", "fork_repository",
            "search_code", "get_file_contents", "search_users",
            "get_issue", "search_issues", "list_issues", "get_issue_comments",
            "get_commit", "list_commits", "list_branches", "list_tags", "get_tag",
            "get_pull_request", "list_pull_requests", "get_pull_request_files",
            "get_pull_request_status", "get_pull_request_comments"
        ]:
            # Test specific tool
            tool_name = sys.argv[1]
            server_url = sys.argv[2] if len(sys.argv) > 2 else "http://localhost:5001"
            asyncio.run(test_specific_tool(tool_name, server_url))
        else:
            # First argument is server URL, test all tools
            server_url = sys.argv[1]
            asyncio.run(test_all_tools(server_url))
    else:
        # Test all tools with default server
        asyncio.run(test_all_tools())