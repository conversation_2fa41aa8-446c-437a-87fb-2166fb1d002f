"""
Test suite for data type conversion in Zoho CRM integration.
Tests the _process_record_data method for proper type handling.
"""

import pytest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from src.zoho_service import ZohoCRMService
from src.config import Settings
from unittest.mock import Mock, patch
from zohocrmsdk.src.com.zoho.crm.api.util import Choice


class TestDataTypeConversion:
    """Test cases for data type conversion in Zoho CRM service"""

    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing"""
        settings = Mock(spec=Settings)
        settings.zoho_client_id = "test_client_id"
        settings.zoho_client_secret = "test_client_secret"
        settings.zoho_refresh_token = "test_refresh_token"
        settings.zoho_environment = "US"
        return settings

    @pytest.fixture
    def zoho_service(self, mock_settings):
        """Fixture providing ZohoCRMService instance"""
        with patch("src.zoho_service.Initializer.initialize"):
            service = ZohoCRMService(mock_settings)
            service.is_initialized = True
            return service

    def test_annual_revenue_int_to_float_conversion(self, zoho_service):
        """Test that Annual_Revenue integer is converted to float"""
        record_data = {
            "Last_Name": "Smith",
            "Company": "Test Corp",
            "Annual_Revenue": 100000,  # Integer input
        }

        processed_data = zoho_service._process_record_data("Leads", record_data)

        # Verify Annual_Revenue is converted to float
        assert isinstance(processed_data["Annual_Revenue"], float)
        assert processed_data["Annual_Revenue"] == 100000.0
        assert processed_data["Last_Name"] == "Smith"
        assert processed_data["Company"] == "Test Corp"

    def test_annual_revenue_float_unchanged(self, zoho_service):
        """Test that Annual_Revenue float remains unchanged"""
        record_data = {
            "Last_Name": "Smith",
            "Company": "Test Corp",
            "Annual_Revenue": 100000.5,  # Float input
        }

        processed_data = zoho_service._process_record_data("Leads", record_data)

        # Verify Annual_Revenue remains float
        assert isinstance(processed_data["Annual_Revenue"], float)
        assert processed_data["Annual_Revenue"] == 100000.5

    def test_annual_revenue_string_conversion(self, zoho_service):
        """Test that Annual_Revenue string is converted to float"""
        record_data = {
            "Last_Name": "Smith",
            "Company": "Test Corp",
            "Annual_Revenue": "100000",  # String input
        }

        processed_data = zoho_service._process_record_data("Leads", record_data)

        # Verify Annual_Revenue is converted to float
        assert isinstance(processed_data["Annual_Revenue"], float)
        assert processed_data["Annual_Revenue"] == 100000.0

    def test_annual_revenue_invalid_value_unchanged(self, zoho_service):
        """Test that invalid Annual_Revenue value remains unchanged"""
        record_data = {
            "Last_Name": "Smith",
            "Company": "Test Corp",
            "Annual_Revenue": "invalid_value",  # Invalid input
        }

        processed_data = zoho_service._process_record_data("Leads", record_data)

        # Verify Annual_Revenue remains unchanged when conversion fails
        assert processed_data["Annual_Revenue"] == "invalid_value"

    def test_annual_revenue_none_value_unchanged(self, zoho_service):
        """Test that None Annual_Revenue value remains unchanged"""
        record_data = {
            "Last_Name": "Smith",
            "Company": "Test Corp",
            "Annual_Revenue": None,  # None input
        }

        processed_data = zoho_service._process_record_data("Leads", record_data)

        # Verify Annual_Revenue remains None
        assert processed_data["Annual_Revenue"] is None

    def test_deals_amount_conversion(self, zoho_service):
        """Test that Deals Amount field is converted to float"""
        record_data = {
            "Deal_Name": "Test Deal",
            "Amount": 50000,  # Integer input
            "Stage": "Proposal/Price Quote",
        }

        processed_data = zoho_service._process_record_data("Deals", record_data)

        # Verify Amount is converted to float
        assert isinstance(processed_data["Amount"], float)
        assert processed_data["Amount"] == 50000.0

    def test_picklist_field_wrapping_with_float_conversion(self, zoho_service):
        """Test that picklist fields are wrapped and float fields converted simultaneously"""
        # Mock the picklist fields cache
        with patch.object(
            zoho_service, "_get_picklist_fields_cache", return_value=["Lead_Source"]
        ):
            record_data = {
                "Last_Name": "Smith",
                "Company": "Test Corp",
                "Lead_Source": "Website",  # Picklist field
                "Annual_Revenue": 100000,  # Float field
            }

            processed_data = zoho_service._process_record_data("Leads", record_data)

            # Verify picklist field is wrapped in Choice
            assert isinstance(processed_data["Lead_Source"], Choice)
            assert processed_data["Lead_Source"].get_value() == "Website"

            # Verify float field is converted
            assert isinstance(processed_data["Annual_Revenue"], float)
            assert processed_data["Annual_Revenue"] == 100000.0

    def test_non_float_fields_unchanged(self, zoho_service):
        """Test that non-float fields remain unchanged"""
        record_data = {
            "Last_Name": "Smith",
            "Company": "Test Corp",
            "Email": "<EMAIL>",
            "Phone": "******-123-4567",
        }

        processed_data = zoho_service._process_record_data("Leads", record_data)

        # Verify all fields remain unchanged
        assert processed_data["Last_Name"] == "Smith"
        assert processed_data["Company"] == "Test Corp"
        assert processed_data["Email"] == "<EMAIL>"
        assert processed_data["Phone"] == "******-123-4567"

    def test_multiple_float_fields_conversion(self, zoho_service):
        """Test conversion of multiple float fields in different modules"""
        # Test Quotes module with multiple float fields
        record_data = {
            "Quote_Name": "Test Quote",
            "Grand_Total": 1000,  # Integer
            "Sub_Total": 900.50,  # Float
            "Tax": "100",  # String
            "Adjustment": None,  # None
        }

        processed_data = zoho_service._process_record_data("Quotes", record_data)

        # Verify all numeric fields are properly handled
        assert isinstance(processed_data["Grand_Total"], float)
        assert processed_data["Grand_Total"] == 1000.0

        assert isinstance(processed_data["Sub_Total"], float)
        assert processed_data["Sub_Total"] == 900.50

        assert isinstance(processed_data["Tax"], float)
        assert processed_data["Tax"] == 100.0

        assert processed_data["Adjustment"] is None
        assert processed_data["Quote_Name"] == "Test Quote"


if __name__ == "__main__":
    pytest.main([__file__])
