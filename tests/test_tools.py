"""
Test suite for MCP tools functionality.
Tests the FastMCP tools integration with Zoho CRM.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
import asyncio
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from zoho_service import ZohoCRMService
from config import Settings


class TestMCPTools:
    """Test cases for MCP tools"""

    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing"""
        settings = Mock(spec=Settings)
        settings.zoho_client_id = "test_client_id"
        settings.zoho_client_secret = "test_client_secret"
        settings.zoho_refresh_token = "test_refresh_token"
        settings.zoho_environment = "US"
        settings.server_host = "localhost"
        settings.server_port = 8080
        settings.server_path = "/mcp"
        settings.log_level = "INFO"
        return settings

    @pytest.fixture
    def mock_zoho_service(self, mock_settings):
        """Mock Zoho service for testing"""
        service = Mock(spec=ZohoCRMService)
        service.settings = mock_settings
        service.is_initialized = True
        service.is_authenticated = AsyncMock(return_value=True)
        service.refresh_authentication = AsyncMock()
        return service

    @pytest.mark.asyncio
    async def test_create_record_tool_success(self, mock_zoho_service):
        """Test create_record tool with successful response"""
        # Mock successful response
        mock_zoho_service.create_record = AsyncMock(
            return_value={
                "id": "*********",
                "message": "Record created successfully",
                "status": "success",
                "details": {"created_time": "2025-01-07T12:00:00Z"},
            }
        )

        # Import and patch the global zoho_service
        with patch("main.zoho_service", mock_zoho_service):
            import main

            # Get the underlying function from the FastMCP tool
            create_record_func = main.create_record.fn

            result = await create_record_func(
                module="Leads",
                record_data={
                    "Last_Name": "Test Lead",
                    "Company": "Test Company",
                    "Email": "<EMAIL>",
                },
                duplicate_check=True,
                trigger_workflows=False,
            )

        # Verify result
        assert result["success"] is True
        assert result["record_id"] == "*********"
        assert result["message"] == "Record created successfully"
        assert "details" in result

        # Verify service was called correctly
        mock_zoho_service.create_record.assert_called_once_with(
            module="Leads",
            record_data={
                "Last_Name": "Test Lead",
                "Company": "Test Company",
                "Email": "<EMAIL>",
            },
            duplicate_check=True,
            trigger_workflows=False,
            approval_process=False,
        )

    @pytest.mark.asyncio
    async def test_create_record_tool_error(self, mock_zoho_service):
        """Test create_record tool with error response"""
        # Mock error response
        mock_zoho_service.create_record = AsyncMock(
            side_effect=Exception("API Error: Invalid field value")
        )

        with patch("main.zoho_service", mock_zoho_service):
            import main

            create_record_func = main.create_record.fn

            result = await create_record_func(
                module="Leads", record_data={"Invalid_Field": "value"}
            )

        # Verify error handling
        assert result["success"] is False
        assert "API Error: Invalid field value" in result["error"]
        assert result["error_type"] == "Exception"

    @pytest.mark.asyncio
    async def test_fetch_records_tool_success(self, mock_zoho_service):
        """Test fetch_records tool with successful response"""
        # Mock successful response
        mock_zoho_service.fetch_records = AsyncMock(
            return_value={
                "records": [
                    {"id": "123", "Last_Name": "Smith", "Company": "ABC Corp"},
                    {"id": "456", "Last_Name": "Jones", "Company": "XYZ Inc"},
                ],
                "total_count": 2,
                "has_more": False,
            }
        )

        with patch("main.zoho_service", mock_zoho_service):
            import main

            fetch_records_func = main.fetch_records.fn

            result = await fetch_records_func(
                module="Leads",
                fields=["Last_Name", "Company"],
                criteria={"Lead_Status": "Open"},
                page=1,
                per_page=50,
            )

        # Verify result
        assert result["success"] is True
        assert len(result["records"]) == 2
        assert result["count"] == 2
        assert result["page"] == 1
        assert result["per_page"] == 50
        assert result["has_more"] is False

        # Verify service was called correctly
        mock_zoho_service.fetch_records.assert_called_once_with(
            module="Leads",
            fields=["Last_Name", "Company"],
            criteria={"Lead_Status": "Open"},
            sort_by=None,
            sort_order="asc",
            page=1,
            per_page=50,
            max_records=None,
        )

    @pytest.mark.asyncio
    async def test_fetch_records_tool_empty_result(self, mock_zoho_service):
        """Test fetch_records tool with empty result"""
        # Mock empty response
        mock_zoho_service.fetch_records = AsyncMock(
            return_value={"records": [], "total_count": 0, "has_more": False}
        )

        with patch("main.zoho_service", mock_zoho_service):
            import main

            fetch_records_func = main.fetch_records.fn

            result = await fetch_records_func(module="Leads")

        # Verify result
        assert result["success"] is True
        assert result["records"] == []
        assert result["count"] == 0

    @pytest.mark.asyncio
    async def test_search_records_tool_success(self, mock_zoho_service):
        """Test search_records tool with successful response"""
        # Mock successful response
        mock_zoho_service.search_records = AsyncMock(
            return_value={
                "Leads": [{"id": "123", "Last_Name": "Smith", "Company": "ABC Corp"}],
                "Contacts": [
                    {"id": "456", "Last_Name": "Jones", "Account_Name": "XYZ Inc"}
                ],
            }
        )

        with patch("main.zoho_service", mock_zoho_service):
            import main

            search_records_func = main.search_records.fn

            result = await search_records_func(
                modules=["Leads", "Contacts"],
                search_text="Smith",
                fields=["Last_Name", "Company"],
                max_results=100,
            )

        # Verify result
        assert result["success"] is True
        assert "Leads" in result["results"]
        assert "Contacts" in result["results"]
        assert result["total_found"] == 2
        assert result["searched_modules"] == ["Leads", "Contacts"]

        # Verify service was called correctly
        mock_zoho_service.search_records.assert_called_once_with(
            modules=["Leads", "Contacts"],
            search_text="Smith",
            fields=["Last_Name", "Company"],
            criteria=None,
            max_results=100,
        )

    @pytest.mark.asyncio
    async def test_search_records_tool_error(self, mock_zoho_service):
        """Test search_records tool with error response"""
        # Mock error response
        mock_zoho_service.search_records = AsyncMock(
            side_effect=Exception("Search failed")
        )

        with patch("main.zoho_service", mock_zoho_service):
            import main

            search_records_func = main.search_records.fn

            result = await search_records_func(modules=["Leads"], search_text="test")

        # Verify error handling
        assert result["success"] is False
        assert "Search failed" in result["error"]
        assert result["error_type"] == "Exception"

    @pytest.mark.asyncio
    async def test_get_module_schema_resource_success(self, mock_zoho_service):
        """Test module schema resource with successful response"""
        # Mock successful response
        mock_zoho_service.get_module_schema = AsyncMock(
            return_value={
                "api_name": "Leads",
                "module_name": "Leads",
                "plural_label": "Leads",
                "singular_label": "Lead",
                "creatable": True,
                "updatable": True,
                "deletable": True,
                "viewable": True,
                "modified_time": "2025-01-07T12:00:00Z",
            }
        )

        with patch("main.zoho_service", mock_zoho_service):
            import main

            get_module_schema_func = main.get_module_schema.fn

            result = await get_module_schema_func("Leads")

        # Verify result
        assert result["module"] == "Leads"
        assert "schema" in result
        assert result["schema"]["api_name"] == "Leads"
        assert result["last_updated"] == "2025-01-07T12:00:00Z"

    @pytest.mark.asyncio
    async def test_get_module_fields_resource_success(self, mock_zoho_service):
        """Test module fields resource with successful response"""
        # Mock successful response
        mock_zoho_service.get_module_fields = AsyncMock(
            return_value=[
                {
                    "api_name": "Last_Name",
                    "field_label": "Last Name",
                    "data_type": "text",
                    "mandatory": True,
                    "read_only": False,
                    "custom_field": False,
                },
                {
                    "api_name": "Lead_Status",
                    "field_label": "Lead Status",
                    "data_type": "picklist",
                    "mandatory": False,
                    "read_only": False,
                    "custom_field": False,
                    "pick_list_values": [
                        {"display_value": "Open", "actual_value": "Open"},
                        {"display_value": "Qualified", "actual_value": "Qualified"},
                    ],
                },
            ]
        )

        with patch("main.zoho_service", mock_zoho_service):
            import main

            get_module_fields_func = main.get_module_fields.fn

            result = await get_module_fields_func("Leads")

        # Verify result
        assert result["module"] == "Leads"
        assert result["field_count"] == 2
        assert len(result["fields"]) == 2
        assert result["fields"][0]["api_name"] == "Last_Name"
        assert result["fields"][1]["api_name"] == "Lead_Status"

    @pytest.mark.asyncio
    async def test_get_picklist_values_resource_success(self, mock_zoho_service):
        """Test picklist values resource with successful response"""
        # Mock successful response
        mock_zoho_service.get_picklist_values = AsyncMock(
            return_value=[
                {
                    "display_value": "Open - Not Contacted",
                    "actual_value": "Open - Not Contacted",
                },
                {
                    "display_value": "Working - Contacted",
                    "actual_value": "Working - Contacted",
                },
                {
                    "display_value": "Closed - Converted",
                    "actual_value": "Closed - Converted",
                },
            ]
        )

        with patch("main.zoho_service", mock_zoho_service):
            import main

            get_picklist_values_func = main.get_picklist_values.fn

            result = await get_picklist_values_func("Leads", "Lead_Status")

        # Verify result
        assert result["module"] == "Leads"
        assert result["field"] == "Lead_Status"
        assert len(result["values"]) == 3
        assert result["values"][0]["display_value"] == "Open - Not Contacted"

    @pytest.mark.asyncio
    async def test_resource_error_handling(self, mock_zoho_service):
        """Test resource error handling"""
        # Mock error response
        mock_zoho_service.get_module_schema = AsyncMock(
            side_effect=Exception("Module not found")
        )

        with patch("main.zoho_service", mock_zoho_service):
            import main

            get_module_schema_func = main.get_module_schema.fn

            result = await get_module_schema_func("InvalidModule")

        # Verify error handling
        assert "error" in result
        assert result["module"] == "InvalidModule"
        assert "Module not found" in result["error"]


if __name__ == "__main__":
    pytest.main([__file__])
