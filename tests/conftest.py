"""
Pytest configuration and shared fixtures for the test suite.
"""

import pytest
import asyncio
import sys
import os
from unittest.mock import Mock, patch

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from src.config import Settings
from src.zoho_service import ZohoCRMService


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_settings():
    """Mock settings for testing"""
    settings = Mock(spec=Settings)
    settings.zoho_client_id = "test_client_id"
    settings.zoho_client_secret = "test_client_secret"
    settings.zoho_refresh_token = "test_refresh_token"
    settings.zoho_environment = "US"
    settings.server_host = "localhost"
    settings.server_port = 8080
    settings.server_path = "/mcp"
    settings.log_level = "INFO"
    return settings


@pytest.fixture
def mock_settings_invalid():
    """Mock invalid settings for testing error scenarios"""
    settings = Mock(spec=Settings)
    settings.zoho_client_id = ""
    settings.zoho_client_secret = ""
    settings.zoho_refresh_token = ""
    settings.zoho_environment = "US"
    settings.server_host = "localhost"
    settings.server_port = 8080
    settings.server_path = "/mcp"
    settings.log_level = "INFO"
    return settings


@pytest.fixture
def mock_zoho_service_initialized(mock_settings):
    """Mock initialized Zoho service for testing"""
    with patch("src.zoho_service.Initializer.initialize"):
        service = ZohoCRMService(mock_settings)
        service.is_initialized = True
        return service


@pytest.fixture
def mock_zoho_service_uninitialized(mock_settings_invalid):
    """Mock uninitialized Zoho service for testing"""
    with patch(
        "src.zoho_service.Initializer.initialize", side_effect=Exception("Auth failed")
    ):
        service = ZohoCRMService(mock_settings_invalid)
        return service


@pytest.fixture
def sample_lead_data():
    """Sample lead data for testing"""
    return {
        "Last_Name": "Test Lead",
        "Company": "Test Company",
        "Email": "<EMAIL>",
        "Phone": "******-0123",
        "Lead_Status": "Open - Not Contacted",
        "Lead_Source": "Website",
    }


@pytest.fixture
def sample_contact_data():
    """Sample contact data for testing"""
    return {
        "Last_Name": "Test Contact",
        "First_Name": "John",
        "Email": "<EMAIL>",
        "Phone": "******-0124",
        "Account_Name": "Test Account",
    }


@pytest.fixture
def sample_account_data():
    """Sample account data for testing"""
    return {
        "Account_Name": "Test Account",
        "Website": "https://testaccount.com",
        "Phone": "******-0125",
        "Industry": "Technology",
    }


@pytest.fixture
def mock_zoho_record():
    """Mock Zoho record object"""
    record = Mock()
    record.get_key_values.return_value = {
        "id": "*********",
        "Last_Name": "Test",
        "Company": "Test Corp",
        "Email": "<EMAIL>",
        "Created_Time": "2025-01-07T12:00:00Z",
        "Modified_Time": "2025-01-07T12:00:00Z",
    }
    return record


@pytest.fixture
def mock_zoho_success_response():
    """Mock successful Zoho API response"""
    response = Mock()
    response.get_details.return_value = {"id": "*********"}
    response.get_message.return_value = Mock()
    response.get_message.return_value.get_value.return_value = (
        "Record created successfully"
    )
    response.get_status.return_value = Mock()
    response.get_status.return_value.get_value.return_value = "success"
    return response


@pytest.fixture
def mock_zoho_error_response():
    """Mock error Zoho API response"""
    response = Mock()
    response.get_message.return_value = Mock()
    response.get_message.return_value.get_value.return_value = "Invalid field value"
    response.get_code.return_value = Mock()
    response.get_code.return_value.get_value.return_value = "INVALID_DATA"
    return response


@pytest.fixture
def mock_zoho_field():
    """Mock Zoho field object"""
    field = Mock()
    field.get_api_name.return_value = "Last_Name"
    field.get_field_label.return_value = "Last Name"
    field.get_data_type.return_value = "text"
    field.get_mandatory.return_value = True
    field.get_read_only.return_value = False
    field.get_custom_field.return_value = False
    field.get_pick_list_values.return_value = None
    return field


@pytest.fixture
def mock_zoho_picklist_field():
    """Mock Zoho picklist field object"""
    field = Mock()
    field.get_api_name.return_value = "Lead_Status"
    field.get_field_label.return_value = "Lead Status"
    field.get_data_type.return_value = "picklist"
    field.get_mandatory.return_value = False
    field.get_read_only.return_value = False
    field.get_custom_field.return_value = False

    # Mock picklist values
    picklist_value = Mock()
    picklist_value.get_display_value.return_value = "Open - Not Contacted"
    picklist_value.get_actual_value.return_value = "Open - Not Contacted"
    field.get_pick_list_values.return_value = [picklist_value]

    return field


@pytest.fixture
def mock_zoho_module():
    """Mock Zoho module object"""
    module = Mock()
    module.get_api_name.return_value = "Leads"
    module.get_module_name.return_value = "Leads"
    module.get_plural_label.return_value = "Leads"
    module.get_singular_label.return_value = "Lead"
    module.get_creatable.return_value = True
    module.get_updatable.return_value = True
    module.get_deletable.return_value = True
    module.get_viewable.return_value = True
    return module


class MockZohoSDKComponents:
    """Helper class to provide mock Zoho SDK components"""

    @staticmethod
    def mock_successful_create_response():
        """Create a mock successful create response"""
        response = Mock()
        response_object = Mock()
        action_response = Mock()

        action_response.get_details.return_value = {"id": "*********"}
        action_response.get_message.return_value = Mock()
        action_response.get_message.return_value.get_value.return_value = (
            "Record created"
        )
        action_response.get_status.return_value = Mock()
        action_response.get_status.return_value.get_value.return_value = "success"

        response_object.get_data.return_value = [action_response]
        response.get_object.return_value = response_object

        return response, response_object, action_response

    @staticmethod
    def mock_successful_fetch_response(records_data):
        """Create a mock successful fetch response"""
        response = Mock()
        response_object = Mock()
        mock_records = []

        for record_data in records_data:
            record = Mock()
            record.get_key_values.return_value = record_data
            mock_records.append(record)

        response_object.get_data.return_value = mock_records
        response.get_object.return_value = response_object

        return response, response_object, mock_records


@pytest.fixture
def mock_sdk_components():
    """Fixture providing mock SDK components helper"""
    return MockZohoSDKComponents()


# Test data constants
TEST_MODULES = ["Leads", "Contacts", "Accounts", "Deals", "Tasks", "Events"]

TEST_LEAD_FIELDS = [
    "Last_Name",
    "First_Name",
    "Company",
    "Email",
    "Phone",
    "Lead_Status",
    "Lead_Source",
    "Industry",
    "Website",
]

TEST_CONTACT_FIELDS = [
    "Last_Name",
    "First_Name",
    "Email",
    "Phone",
    "Account_Name",
    "Title",
    "Department",
    "Mailing_Street",
    "Mailing_City",
]

TEST_PICKLIST_VALUES = [
    {"display_value": "Open - Not Contacted", "actual_value": "Open - Not Contacted"},
    {"display_value": "Working - Contacted", "actual_value": "Working - Contacted"},
    {"display_value": "Closed - Converted", "actual_value": "Closed - Converted"},
    {
        "display_value": "Closed - Not Converted",
        "actual_value": "Closed - Not Converted",
    },
]
