"""
Tests for the authentication system.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
from fastapi import Request
from fastapi.responses import JSONResponse

from src.auth.models import TokenInfo, create_zoho_auth_error
from src.auth.validator import TokenValidator
from src.auth.middleware import ZohoAuthMiddleware
from src.auth.zoho_provider import ZohoOAuthProvider
from src.auth.exceptions import (
    TokenValidationError,
    TokenExpiredError,
    InsufficientScopesError,
    MissingAuthHeaderError,
    InvalidAuthHeaderError,
)


class TestTokenValidator:
    """Test token validation functionality."""

    @pytest.fixture
    def validator(self):
        return TokenValidator(
            client_id="test_client_id",
            client_secret="test_client_secret",
            environment="US",
            cache_ttl=300,
        )

    @pytest.mark.asyncio
    async def test_validate_valid_token(self, validator):
        """Test validation of a valid token."""
        mock_response = {
            "active": True,
            "exp": int((datetime.now() + timedelta(hours=1)).timestamp()),
            "scope": "ZohoCRM.modules.ALL ZohoCRM.settings.ALL",
            "sub": "test_user_id",
            "client_id": "test_client_id",
        }

        with patch("httpx.AsyncClient") as mock_client:
            mock_response_obj = Mock()
            mock_response_obj.status_code = 200
            mock_response_obj.json.return_value = mock_response

            mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                return_value=mock_response_obj
            )

            token_info = await validator.validate_token("valid_token")

            assert token_info.is_valid is True
            assert token_info.user_id == "test_user_id"
            assert "ZohoCRM.modules.ALL" in token_info.scopes

    @pytest.mark.asyncio
    async def test_validate_expired_token(self, validator):
        """Test validation of an expired token."""
        mock_response = {
            "active": True,
            "exp": int((datetime.now() - timedelta(hours=1)).timestamp()),
            "scope": "ZohoCRM.modules.ALL",
            "sub": "test_user_id",
        }

        with patch("httpx.AsyncClient") as mock_client:
            mock_response_obj = Mock()
            mock_response_obj.status_code = 200
            mock_response_obj.json.return_value = mock_response

            mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                return_value=mock_response_obj
            )

            with pytest.raises(TokenExpiredError):
                await validator.validate_token("expired_token")

    @pytest.mark.asyncio
    async def test_validate_insufficient_scopes(self, validator):
        """Test validation with insufficient scopes."""
        mock_response = {
            "active": True,
            "exp": int((datetime.now() + timedelta(hours=1)).timestamp()),
            "scope": "ZohoCRM.modules.READ",
            "sub": "test_user_id",
        }

        with patch("httpx.AsyncClient") as mock_client:
            mock_response_obj = Mock()
            mock_response_obj.status_code = 200
            mock_response_obj.json.return_value = mock_response

            mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                return_value=mock_response_obj
            )

            required_scopes = ["ZohoCRM.modules.ALL", "ZohoCRM.settings.ALL"]

            with pytest.raises(InsufficientScopesError):
                await validator.validate_token("limited_token", required_scopes)


class TestZohoOAuthProvider:
    """Test Zoho OAuth provider functionality."""

    @pytest.fixture
    def provider(self):
        return ZohoOAuthProvider(
            client_id="test_client_id",
            client_secret="test_client_secret",
            environment="US",
        )

    def test_generate_pkce_pair(self, provider):
        """Test PKCE pair generation."""
        pkce_pair = provider.generate_pkce_pair()

        assert "code_verifier" in pkce_pair
        assert "code_challenge" in pkce_pair
        assert len(pkce_pair["code_verifier"]) >= 43
        assert len(pkce_pair["code_challenge"]) >= 43

    def test_get_authorization_url(self, provider):
        """Test authorization URL generation."""
        url = provider.get_authorization_url(
            scopes=["ZohoCRM.modules.ALL"],
            state="test_state",
            code_challenge="test_challenge",
        )

        assert "accounts.zoho.com" in url
        assert "client_id=test_client_id" in url
        assert "state=test_state" in url
        assert "code_challenge=test_challenge" in url
        assert "ZohoCRM.modules.ALL" in url

    def test_create_authorization_request(self, provider):
        """Test complete authorization request creation."""
        auth_request = provider.create_authorization_request()

        assert "authorization_url" in auth_request
        assert "state" in auth_request
        assert "code_verifier" in auth_request
        assert "code_challenge" in auth_request

        # Verify URL contains required parameters
        url = auth_request["authorization_url"]
        assert "code_challenge=" in url
        assert "state=" in url


class TestZohoAuthMiddleware:
    """Test authentication middleware."""

    @pytest.fixture
    def middleware(self):
        return ZohoAuthMiddleware(
            client_id="test_client_id",
            client_secret="test_client_secret",
            environment="US",
            exclude_paths=["/auth/authorize", "/health"],
        )

    @pytest.fixture
    def mock_request(self):
        request = Mock(spec=Request)
        request.url.path = "/api/test"
        request.headers = {}
        request.state = Mock()
        return request

    @pytest.mark.asyncio
    async def test_missing_auth_header(self, middleware, mock_request):
        """Test request without Authorization header."""

        async def mock_call_next(request):
            return Mock()

        response = await middleware(mock_request, mock_call_next)

        assert isinstance(response, JSONResponse)
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_invalid_auth_header_format(self, middleware, mock_request):
        """Test request with invalid Authorization header format."""
        mock_request.headers = {"Authorization": "Invalid token_here"}

        async def mock_call_next(request):
            return Mock()

        response = await middleware(mock_request, mock_call_next)

        assert isinstance(response, JSONResponse)
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_excluded_path_skips_auth(self, middleware):
        """Test that excluded paths skip authentication."""
        mock_request = Mock(spec=Request)
        mock_request.url.path = "/auth/authorize"

        call_next_called = False

        async def mock_call_next(request):
            nonlocal call_next_called
            call_next_called = True
            return Mock()

        await middleware(mock_request, mock_call_next)
        assert call_next_called is True

    @pytest.mark.asyncio
    async def test_valid_token_passes_through(self, middleware, mock_request):
        """Test that valid token allows request to pass through."""
        mock_request.headers = {"Authorization": "Bearer valid_token"}

        # Mock successful token validation
        mock_token_info = TokenInfo(
            access_token="valid_token",
            is_valid=True,
            scopes=["ZohoCRM.modules.ALL", "ZohoCRM.settings.ALL"],
            user_id="test_user",
        )

        with patch.object(
            middleware.validator, "validate_token", return_value=mock_token_info
        ):
            call_next_called = False

            async def mock_call_next(request):
                nonlocal call_next_called
                call_next_called = True
                # Verify token info was added to request state
                assert hasattr(request.state, "token_info")
                assert request.state.authenticated is True
                return Mock()

            await middleware(mock_request, mock_call_next)
            assert call_next_called is True


class TestAuthModels:
    """Test authentication data models."""

    def test_create_zoho_auth_error(self):
        """Test creation of structured auth error."""
        auth_error = create_zoho_auth_error(
            authorization_url="/auth/authorize", environment="US"
        )

        error_dict = auth_error.to_dict()

        assert error_dict["error"]["code"] == "AUTHENTICATION_REQUIRED"
        assert "authentication_requirements" in error_dict["error"]["details"]

        requirements = error_dict["error"]["details"]["authentication_requirements"]
        assert len(requirements) == 1

        req = requirements[0]
        assert req["provider"] == "zoho"
        assert req["auth_type"] == "bearer"
        assert req["header_name"] == "Authorization"
        assert "ZohoCRM.modules.ALL" in req["required_scopes"]
        assert "accounts.zoho.com" in req["oauth_endpoints"]["authorization"]

    def test_token_info_model(self):
        """Test TokenInfo model."""
        token_info = TokenInfo(
            access_token="test_token",
            is_valid=True,
            expires_at=datetime.now() + timedelta(hours=1),
            scopes=["ZohoCRM.modules.ALL"],
            user_id="test_user",
            client_id="test_client",
        )

        assert token_info.access_token == "test_token"
        assert token_info.is_valid is True
        assert "ZohoCRM.modules.ALL" in token_info.scopes
        assert token_info.user_id == "test_user"


@pytest.mark.integration
class TestAuthIntegration:
    """Integration tests for the complete auth system."""

    @pytest.mark.asyncio
    async def test_complete_auth_flow_simulation(self):
        """Simulate a complete authentication flow."""
        # 1. Create OAuth provider
        provider = ZohoOAuthProvider(
            client_id="test_client_id",
            client_secret="test_client_secret",
            environment="US",
        )

        # 2. Generate authorization request
        auth_request = provider.create_authorization_request()
        assert "authorization_url" in auth_request

        # 3. Simulate token validation (would normally happen after OAuth callback)
        validator = TokenValidator(
            client_id="test_client_id",
            client_secret="test_client_secret",
            environment="US",
        )

        # Mock successful validation response
        mock_response = {
            "active": True,
            "exp": int((datetime.now() + timedelta(hours=1)).timestamp()),
            "scope": "ZohoCRM.modules.ALL ZohoCRM.settings.ALL",
            "sub": "test_user_id",
            "client_id": "test_client_id",
        }

        with patch("httpx.AsyncClient") as mock_client:
            mock_response_obj = Mock()
            mock_response_obj.status_code = 200
            mock_response_obj.json.return_value = mock_response

            mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                return_value=mock_response_obj
            )

            token_info = await validator.validate_token("test_access_token")
            assert token_info.is_valid is True
            assert token_info.user_id == "test_user_id"


if __name__ == "__main__":
    pytest.main([__file__])
