"""
Test suite for Zoho CRM integration.
Tests the ZohoCRMService class and SDK integration.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from src.zoho_service import ZohoCRMService
from src.config import Settings


class TestZohoCRMService:
    """Test cases for Zoho CRM service integration"""

    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing"""
        settings = Mock(spec=Settings)
        settings.zoho_client_id = "test_client_id"
        settings.zoho_client_secret = "test_client_secret"
        settings.zoho_refresh_token = "test_refresh_token"
        settings.zoho_environment = "US"
        return settings

    @pytest.fixture
    def mock_settings_invalid(self):
        """Mock invalid settings for testing error scenarios"""
        settings = Mock(spec=Settings)
        settings.zoho_client_id = ""
        settings.zoho_client_secret = ""
        settings.zoho_refresh_token = ""
        settings.zoho_environment = "US"
        return settings

    @pytest.fixture
    def zoho_service(self, mock_settings):
        """Fixture providing ZohoCRMService instance"""
        with patch("src.zoho_service.Initializer.initialize"):
            service = ZohoCRMService(mock_settings)
            service.is_initialized = True  # Force initialization for testing
            return service

    @pytest.fixture
    def zoho_service_uninitialized(self, mock_settings_invalid):
        """Fixture providing uninitialized ZohoCRMService instance"""
        with patch(
            "src.zoho_service.Initializer.initialize",
            side_effect=Exception("Auth failed"),
        ):
            service = ZohoCRMService(mock_settings_invalid)
            return service

    def test_zoho_service_initialization_success(self, mock_settings):
        """Test successful Zoho service initialization"""
        with patch("src.zoho_service.Initializer.initialize") as mock_init:
            service = ZohoCRMService(mock_settings)

            # Verify initialization was attempted
            mock_init.assert_called_once()
            assert service.settings == mock_settings
            assert service.is_initialized is True

    def test_zoho_service_initialization_failure(self, mock_settings_invalid):
        """Test Zoho service initialization failure"""
        with patch(
            "src.zoho_service.Initializer.initialize",
            side_effect=Exception("Invalid credentials"),
        ):
            service = ZohoCRMService(mock_settings_invalid)

            assert service.settings == mock_settings_invalid
            assert service.is_initialized is False

    @pytest.mark.asyncio
    async def test_is_authenticated_success(self, zoho_service):
        """Test authentication check when initialized"""
        result = await zoho_service.is_authenticated()
        assert result is True

    @pytest.mark.asyncio
    async def test_is_authenticated_failure(self, zoho_service_uninitialized):
        """Test authentication check when not initialized"""
        result = await zoho_service_uninitialized.is_authenticated()
        assert result is False

    @pytest.mark.asyncio
    async def test_refresh_authentication_success(self, zoho_service_uninitialized):
        """Test authentication refresh"""
        with patch.object(zoho_service_uninitialized, "_initialize_sdk") as mock_init:
            await zoho_service_uninitialized.refresh_authentication()
            mock_init.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_record_success(self, zoho_service):
        """Test successful record creation"""
        # Mock the response processing method directly
        expected_result = {
            "id": "*********",
            "message": "Record created successfully",
            "status": "success",
            "details": {"created_time": "2025-01-07T12:00:00Z"},
        }

        # Mock the sync function that would be called
        def mock_create_sync():
            return expected_result

        with patch.object(
            zoho_service, "_process_create_response", return_value=expected_result
        ):
            with patch("asyncio.get_event_loop") as mock_loop:
                mock_executor = Mock()
                mock_executor.return_value = expected_result
                mock_loop.return_value.run_in_executor = AsyncMock(
                    return_value=expected_result
                )

                result = await zoho_service.create_record(
                    module="Leads",
                    record_data={"Last_Name": "Test", "Company": "Test Corp"},
                    duplicate_check=True,
                )

        # Verify result
        assert result["id"] == "*********"
        assert result["message"] == "Record created successfully"
        assert result["status"] == "success"

    @pytest.mark.asyncio
    async def test_create_record_error(self, zoho_service):
        """Test record creation with error"""
        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.run_in_executor = AsyncMock(
                side_effect=Exception("API Error: Invalid field")
            )

            with pytest.raises(Exception) as exc_info:
                await zoho_service.create_record(
                    module="Leads", record_data={"Invalid_Field": "value"}
                )

            assert "API Error: Invalid field" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_fetch_records_success(self, zoho_service):
        """Test successful record fetching"""
        expected_result = {
            "records": [
                {"id": "123", "Last_Name": "Smith", "Company": "ABC Corp"},
                {"id": "456", "Last_Name": "Jones", "Company": "XYZ Inc"},
            ],
            "total_count": 2,
            "has_more": False,
        }

        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.run_in_executor = AsyncMock(
                return_value=expected_result
            )

            result = await zoho_service.fetch_records(
                module="Leads", fields=["Last_Name", "Company"], page=1, per_page=50
            )

        # Verify result
        assert len(result["records"]) == 2
        assert result["records"][0]["Last_Name"] == "Smith"
        assert result["records"][1]["Last_Name"] == "Jones"
        assert result["total_count"] == 2

    @pytest.mark.asyncio
    async def test_fetch_records_empty(self, zoho_service):
        """Test fetching records with empty result"""
        expected_result = {"records": [], "total_count": 0, "has_more": False}

        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.run_in_executor = AsyncMock(
                return_value=expected_result
            )

            result = await zoho_service.fetch_records(module="Leads")

        # Verify empty result
        assert result["records"] == []
        assert result["total_count"] == 0
        assert result["has_more"] is False

    @pytest.mark.asyncio
    async def test_search_records_success(self, zoho_service):
        """Test successful record searching"""
        expected_result = {
            "Leads": [{"id": "123", "Last_Name": "Smith", "Company": "ABC Corp"}],
            "Contacts": [
                {"id": "456", "Last_Name": "Jones", "Account_Name": "XYZ Inc"}
            ],
        }

        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.run_in_executor = AsyncMock(
                return_value=expected_result
            )

            result = await zoho_service.search_records(
                modules=["Leads", "Contacts"], search_text="Smith", max_results=100
            )

        # Verify result
        assert "Leads" in result
        assert "Contacts" in result
        assert len(result["Leads"]) == 1
        assert result["Leads"][0]["Last_Name"] == "Smith"

    @pytest.mark.asyncio
    async def test_search_records_module_error(self, zoho_service):
        """Test search records with module error"""
        # This test verifies the error handling within the search method
        expected_result = {"InvalidModule": []}

        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.run_in_executor = AsyncMock(
                return_value=expected_result
            )

            result = await zoho_service.search_records(
                modules=["InvalidModule"], search_text="test"
            )

        # Verify error handling
        assert "InvalidModule" in result
        assert result["InvalidModule"] == []

    @pytest.mark.asyncio
    async def test_get_module_schema_success(self, zoho_service):
        """Test successful module schema retrieval"""
        expected_result = {
            "api_name": "Leads",
            "module_name": "Leads",
            "plural_label": "Leads",
            "singular_label": "Lead",
            "creatable": True,
            "updatable": True,
            "deletable": True,
            "viewable": True,
        }

        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.run_in_executor = AsyncMock(
                return_value=expected_result
            )

            result = await zoho_service.get_module_schema("Leads")

        # Verify result
        assert result["api_name"] == "Leads"
        assert result["module_name"] == "Leads"
        assert result["creatable"] is True
        assert result["updatable"] is True

    @pytest.mark.asyncio
    async def test_get_module_fields_success(self, zoho_service):
        """Test successful module fields retrieval"""
        expected_result = [
            {
                "api_name": "Last_Name",
                "field_label": "Last Name",
                "data_type": "text",
                "mandatory": True,
                "read_only": False,
                "custom_field": False,
            }
        ]

        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.run_in_executor = AsyncMock(
                return_value=expected_result
            )

            result = await zoho_service.get_module_fields("Leads")

        # Verify result
        assert len(result) == 1
        assert result[0]["api_name"] == "Last_Name"
        assert result[0]["field_label"] == "Last Name"
        assert result[0]["mandatory"] is True

    @pytest.mark.asyncio
    async def test_get_picklist_values_success(self, zoho_service):
        """Test successful picklist values retrieval"""
        expected_result = [
            {
                "display_value": "Open - Not Contacted",
                "actual_value": "Open - Not Contacted",
            },
            {
                "display_value": "Working - Contacted",
                "actual_value": "Working - Contacted",
            },
        ]

        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.run_in_executor = AsyncMock(
                return_value=expected_result
            )

            result = await zoho_service.get_picklist_values("Leads", "Lead_Status")

        # Verify result
        assert len(result) == 2
        assert result[0]["display_value"] == "Open - Not Contacted"
        assert result[0]["actual_value"] == "Open - Not Contacted"

    @pytest.mark.asyncio
    async def test_get_picklist_values_no_values(self, zoho_service):
        """Test picklist values retrieval with no values"""
        expected_result = []

        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.run_in_executor = AsyncMock(
                return_value=expected_result
            )

            result = await zoho_service.get_picklist_values("Leads", "Text_Field")

        # Verify empty result
        assert result == []

    @pytest.mark.asyncio
    async def test_service_method_exception_handling(self, zoho_service):
        """Test exception handling in service methods"""
        with patch("asyncio.get_event_loop") as mock_loop:
            mock_loop.return_value.run_in_executor = AsyncMock(
                side_effect=Exception("SDK Error")
            )

            with pytest.raises(Exception) as exc_info:
                await zoho_service.get_module_schema("Leads")

            assert "SDK Error" in str(exc_info.value)

    def test_environment_mapping_basic(self, mock_settings):
        """Test basic environment mapping functionality"""
        # Test that different environments can be set
        environments = ["US", "EU", "IN", "INVALID"]

        for env in environments:
            mock_settings.zoho_environment = env
            with patch("src.zoho_service.Initializer.initialize"):
                service = ZohoCRMService(mock_settings)
                assert service.settings.zoho_environment == env

    def test_service_has_required_methods(self, zoho_service):
        """Test that service has all required methods"""
        required_methods = [
            "is_authenticated",
            "refresh_authentication",
            "create_record",
            "fetch_records",
            "search_records",
            "get_module_schema",
            "get_module_fields",
            "get_picklist_values",
        ]

        for method_name in required_methods:
            assert hasattr(zoho_service, method_name)
            assert callable(getattr(zoho_service, method_name))


if __name__ == "__main__":
    pytest.main([__file__])
