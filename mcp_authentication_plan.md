# MCP Authentication Layer for Zoho CRM Server

## Overview

This plan outlines the implementation of an authentication layer for the Zoho CRM MCP server that provides structured error responses and OAuth 2.1 compliance without persistent token storage.

## Architecture Goals

1. **Stateless Authentication**: No token storage, rely on client-provided tokens
2. **Structured Error Responses**: Clear authentication requirements in error responses
3. **OAuth 2.1 Compliance**: Modern OAuth standards with PKCE support
4. **Zoho-Focused**: Single provider implementation for Zoho CRM
5. **MCP Integration**: Seamless FastMCP middleware integration

## System Architecture

```mermaid
graph TB
    subgraph "MCP Client"
        C[MCP Client]
    end

    subgraph "Authentication Layer"
        AM[Auth Middleware]
        ZP[Zoho Provider]
        TV[Token Validator]
    end

    subgraph "MCP Server"
        MS[MCP Server]
        ZS[Zoho Service]
    end

    subgraph "External Services"
        ZO[Zoho OAuth API]
    end

    C -->|Request + Bearer Token| AM
    AM -->|Validate Token| TV
    TV -->|Introspect Token| ZO
    AM -->|Valid Request| MS
    MS -->|CRM API Calls| ZS
    ZS -->|Authenticated Calls| ZO
    AM -->|Auth Error Response| C
    ZP -->|OAuth Flow| ZO
```

## Authentication Flow

```mermaid
sequenceDiagram
    participant Client as MCP Client
    participant Auth as Auth Middleware
    participant Zoho as Zoho OAuth API
    participant Server as MCP Server

    Client->>Auth: Request without token
    Auth->>Client: 401 + Structured Error Response

    Note over Client: Client handles OAuth flow externally
    Client->>Auth: Request with Bearer token
    Auth->>Zoho: Validate token (introspection)
    Zoho->>Auth: Token validation result

    alt Token Valid
        Auth->>Server: Forward authenticated request
        Server->>Client: Success response
    else Token Invalid
        Auth->>Client: 401 + Structured Error Response
    end
```

## File Structure

```
src/
├── auth/
│   ├── __init__.py
│   ├── middleware.py          # FastMCP auth middleware
│   ├── zoho_provider.py       # Zoho OAuth provider
│   ├── validator.py           # Token validation
│   ├── models.py             # Auth data models
│   └── exceptions.py         # Auth exceptions
├── config.py                 # Updated config
├── main.py                   # Updated main with auth
└── zoho_service.py           # Updated service
```

## Key Components

### 1. Authentication Middleware

```python
class ZohoAuthMiddleware:
    """FastMCP middleware for Zoho OAuth authentication"""

    async def __call__(self, request, call_next):
        # Extract Bearer token from Authorization header
        # Validate token with Zoho introspection endpoint
        # Return structured error if invalid
        # Forward request if valid
```

### 2. Zoho OAuth Provider

```python
class ZohoOAuthProvider:
    """Zoho-specific OAuth 2.1 implementation"""

    def get_authorization_url(self, state: str, code_challenge: str) -> str:
        # Generate PKCE-enabled authorization URL

    async def validate_token(self, access_token: str) -> TokenInfo:
        # Validate token using Zoho's token introspection
```

### 3. Structured Error Response

```python
def create_auth_error_response() -> dict:
    return {
        "error": {
            "code": "AUTHENTICATION_REQUIRED",
            "message": "Authentication required to access this resource",
            "details": {
                "authentication_requirements": [
                    {
                        "provider": "zoho",
                        "auth_type": "bearer",
                        "header_name": "Authorization",
                        "header_format": "Bearer {access_token}",
                        "required_scopes": [
                            "ZohoCRM.modules.ALL",
                            "ZohoCRM.settings.ALL"
                        ],
                        "token_source": "access_token",
                        "authorization_url": "/auth/authorize",
                        "oauth_endpoints": {
                            "authorization": "https://accounts.zoho.com/oauth/v2/auth",
                            "token": "https://accounts.zoho.com/oauth/v2/token",
                            "introspection": "https://accounts.zoho.com/oauth/v2/token/info"
                        }
                    }
                ]
            }
        }
    }
```

## Implementation Details

### Token Validation Strategy

1. **No Storage**: Tokens are validated on each request
2. **Introspection**: Use Zoho's token introspection endpoint
3. **Caching**: Optional short-term validation result caching (5-10 minutes)
4. **Scope Checking**: Validate required scopes for each endpoint

### OAuth 2.1 Features

1. **PKCE**: Proof Key for Code Exchange for security
2. **State Parameter**: CSRF protection
3. **Scope Validation**: Granular permission checking
4. **Token Introspection**: RFC 7662 compliance

### Error Response Structure

All authentication errors follow this structure:

- Clear error codes and messages
- Detailed authentication requirements
- OAuth endpoint information
- Required scopes specification
- Header format examples

## API Endpoints

```
GET  /auth/authorize          # Generate authorization URL with PKCE
GET  /auth/callback           # OAuth callback handler (optional)
POST /auth/validate           # Validate token endpoint
GET  /auth/requirements       # Get authentication requirements
```

## Security Considerations

1. **No Token Storage**: Eliminates storage security risks
2. **Token Introspection**: Real-time validation with Zoho
3. **PKCE**: Protection against authorization code interception
4. **Scope Validation**: Principle of least privilege
5. **Rate Limiting**: Protection against validation abuse

## Integration with Existing Code

### Updated Configuration

```python
class Settings(BaseSettings):
    # Existing Zoho config
    zoho_client_id: str
    zoho_client_secret: str
    zoho_environment: str = "US"

    # New auth config
    auth_enabled: bool = True
    token_validation_cache_ttl: int = 300  # 5 minutes
    required_scopes: List[str] = [
        "ZohoCRM.modules.ALL",
        "ZohoCRM.settings.ALL"
    ]
```

### Updated Main Application

```python
from auth.middleware import ZohoAuthMiddleware

# Add auth middleware to FastMCP
mcp.add_middleware(ZohoAuthMiddleware())
```

## Testing Strategy

1. **Unit Tests**: Token validation logic
2. **Integration Tests**: OAuth flow end-to-end
3. **Error Response Tests**: Structured error format
4. **Security Tests**: Invalid token handling
5. **Performance Tests**: Validation latency

## Benefits

1. **Stateless**: No token storage complexity
2. **Secure**: Real-time token validation
3. **Standards Compliant**: OAuth 2.1 + RFC 7662
4. **Clear Errors**: Structured authentication guidance
5. **Maintainable**: Single provider focus
6. **Scalable**: No storage bottlenecks

## Implementation Phases

### Phase 1: Core Authentication

- Implement Zoho OAuth provider
- Create token validation logic
- Build structured error responses

### Phase 2: MCP Integration

- Develop FastMCP middleware
- Update existing tools with auth
- Add authentication endpoints

### Phase 3: Testing & Refinement

- Comprehensive testing suite
- Performance optimization
- Documentation updates

This architecture provides a clean, secure, and maintainable authentication layer specifically designed for the Zoho CRM MCP server without the complexity of token storage.
