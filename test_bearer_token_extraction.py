#!/usr/bin/env python3
"""
Test Bearer token extraction from FastMCP middleware.

This test verifies that the authentication middleware properly extracts
Bearer tokens and makes them available to MCP tools.
"""

import sys
import os
import asyncio
import logging
from unittest.mock import Mock, AsyncMock

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), "src"))

from auth.middleware import ZohoAuthMiddleware, get_token_info
from auth.models import TokenInfo
from fastapi import Request
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


async def test_bearer_token_extraction():
    """Test that Bearer tokens are properly extracted and stored in request state."""

    print("🧪 Testing Bearer Token Extraction with FastMCP Middleware")
    print("=" * 60)

    # Create mock request with Authorization header
    mock_request = Mock(spec=Request)
    mock_request.url.path = "/mcp/tools/call"
    mock_request.headers = {"Authorization": "Bearer test_access_token_12345"}
    mock_request.state = Mock()

    # Create middleware instance
    middleware = ZohoAuthMiddleware(
        client_id="test_client_id",
        client_secret="test_client_secret",
        environment="US",
        required_scopes=["ZohoCRM.modules.ALL"],
        cache_ttl=300,
    )

    # Mock the token validator to return a valid token
    mock_token_info = TokenInfo(
        access_token="test_access_token_12345",
        is_valid=True,
        expires_at=datetime.now() + timedelta(hours=1),
        scopes=["ZohoCRM.modules.ALL", "ZohoCRM.settings.ALL"],
        user_id="test_user_123",
        client_id="test_client_id",
    )

    # Mock the validator's validate_token method
    middleware.validator.validate_token = AsyncMock(return_value=mock_token_info)

    # Mock call_next function
    async def mock_call_next(request):
        return Mock(status_code=200, content="Success")

    try:
        # Test the middleware authentication
        print("1. Testing middleware authentication...")
        result = await middleware(mock_request, mock_call_next)

        # Verify that token info was stored in request state
        assert hasattr(
            mock_request.state, "token_info"
        ), "Token info not stored in request state"
        assert mock_request.state.token_info == mock_token_info, "Token info mismatch"
        assert (
            mock_request.state.authenticated == True
        ), "Request not marked as authenticated"

        print("   ✅ Middleware successfully extracted and stored token info")

        # Test the get_token_info helper function
        print("2. Testing get_token_info helper function...")
        extracted_token_info = get_token_info(mock_request)

        assert extracted_token_info is not None, "get_token_info returned None"
        assert (
            extracted_token_info.access_token == "test_access_token_12345"
        ), "Access token mismatch"
        assert extracted_token_info.user_id == "test_user_123", "User ID mismatch"
        assert extracted_token_info.is_valid == True, "Token not marked as valid"

        print("   ✅ get_token_info successfully retrieved token info")

        # Test token extraction details
        print("3. Verifying token details...")
        print(f"   • Access Token: {extracted_token_info.access_token}")
        print(f"   • User ID: {extracted_token_info.user_id}")
        print(f"   • Client ID: {extracted_token_info.client_id}")
        print(f"   • Scopes: {extracted_token_info.scopes}")
        print(f"   • Valid: {extracted_token_info.is_valid}")
        print(f"   • Expires At: {extracted_token_info.expires_at}")

        print("\n🎉 All Bearer Token Extraction Tests Passed!")
        print("=" * 60)
        print("✅ The authentication middleware correctly:")
        print("   • Extracts Bearer tokens from Authorization headers")
        print("   • Validates tokens with Zoho")
        print("   • Stores token info in request.state")
        print("   • Makes token info accessible via get_token_info()")
        print(
            "   • Provides all necessary token details for dynamic SDK initialization"
        )

        return True

    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


async def test_missing_auth_header():
    """Test behavior when Authorization header is missing."""

    print("\n🧪 Testing Missing Authorization Header")
    print("-" * 40)

    # Create mock request without Authorization header
    mock_request = Mock(spec=Request)
    mock_request.url.path = "/mcp/tools/call"
    mock_request.headers = {}
    mock_request.state = Mock()

    # Create middleware instance
    middleware = ZohoAuthMiddleware(
        client_id="test_client_id",
        client_secret="test_client_secret",
        environment="US",
    )

    # Mock call_next function
    async def mock_call_next(request):
        return Mock(status_code=200, content="Success")

    try:
        # Test the middleware with missing auth header
        result = await middleware(mock_request, mock_call_next)

        # Should return an error response
        assert result.status_code == 401, "Expected 401 Unauthorized"
        print("   ✅ Middleware correctly returns 401 for missing Authorization header")

        return True

    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False


async def main():
    """Run all tests."""

    print("🚀 Starting Bearer Token Extraction Tests")
    print("=" * 60)

    success = True

    # Test 1: Bearer token extraction
    success &= await test_bearer_token_extraction()

    # Test 2: Missing auth header
    success &= await test_missing_auth_header()

    if success:
        print("\n🎉 All Tests Passed!")
        print("The FastMCP authentication middleware is working correctly.")
        print("\n📋 Next Steps:")
        print("1. Start the MCP server: python src/main.py")
        print("2. Include Authorization header in requests:")
        print("   Authorization: Bearer YOUR_ACCESS_TOKEN")
        print("3. The SDK will initialize automatically for each request")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
