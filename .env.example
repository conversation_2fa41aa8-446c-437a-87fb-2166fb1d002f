# Zoho CRM MCP Server Configuration
# Copy this file to .env and configure your settings

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# Server host and port
SERVER_HOST=127.0.0.1
SERVER_PORT=8000

# Server identification
SERVER_NAME=Zoho CRM MCP Server
SERVER_VERSION=1.0.0

# Debug and logging
SERVER_DEBUG=false
SERVER_LOG_LEVEL=INFO

# =============================================================================
# ZOHO CRM CONFIGURATION
# =============================================================================

# Zoho environment (US, EU, IN, AU, CN, JP)
ZOHO_ENVIRONMENT=US

# OAuth credentials (required for authentication)
ZOHO_CLIENT_ID=your_zoho_client_id_here
ZOHO_CLIENT_SECRET=your_zoho_client_secret_here

# API configuration
ZOHO_API_VERSION=v6
ZOHO_TIMEOUT=30
ZOHO_MAX_RETRIES=3

# Custom base URL (optional - overrides environment-based URL)
# ZOHO_BASE_URL=https://custom.zohoapis.com/crm/v6

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Authentication settings
SECURITY_REQUIRE_AUTH=true
SECURITY_AUTH_HEADER=Authorization
SECURITY_AUTH_SCHEME=Bearer

# Rate limiting (optional)
SECURITY_RATE_LIMIT_ENABLED=false
SECURITY_RATE_LIMIT_REQUESTS=100

# =============================================================================
# CORS CONFIGURATION
# =============================================================================

# CORS settings for web clients
CORS_ALLOW_ORIGINS=*
CORS_ALLOW_METHODS=*
CORS_ALLOW_HEADERS=*
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=600

# =============================================================================
# PRODUCTION DEPLOYMENT SETTINGS
# =============================================================================

# For production deployment, consider these settings:

# Security
# SECURITY_REQUIRE_AUTH=true
# CORS_ALLOW_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# Performance
# SERVER_DEBUG=false
# SERVER_LOG_LEVEL=WARNING
# ZOHO_TIMEOUT=60
# ZOHO_MAX_RETRIES=5

# Rate limiting
# SECURITY_RATE_LIMIT_ENABLED=true
# SECURITY_RATE_LIMIT_REQUESTS=1000

# =============================================================================
# ENVIRONMENT-SPECIFIC URLS (for reference)
# =============================================================================

# US: https://www.zohoapis.com/crm/v6
# EU: https://www.zohoapis.eu/crm/v6
# IN: https://www.zohoapis.in/crm/v6
# AU: https://www.zohoapis.com.au/crm/v6
# CN: https://www.zohoapis.com.cn/crm/v6
# JP: https://www.zohoapis.jp/crm/v6