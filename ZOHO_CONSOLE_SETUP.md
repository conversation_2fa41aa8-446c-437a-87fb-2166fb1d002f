# Zoho Console Setup Guide

This guide walks you through setting up your Zoho CRM application in the Zoho Developer Console with the correct configuration for OAuth.

## Step 1: Create Zoho Developer Account

1. Go to [Zoho Developer Console](https://api-console.zoho.com/)
2. Sign in with your Zoho account (or create one if needed)
3. Accept the developer terms and conditions

## Step 2: Create a New Application

1. Click **"Add Client"** or **"Create Application"**
2. Choose **"Server-based Applications"**
3. Fill in the application details:

### Application Information

- **Client Name**: `Zoho CRM MCP Server` (or your preferred name)
- **Homepage URL**: `http://localhost:8000` (for development)
- **Authorized Redirect URIs**:
  ```
  http://localhost:8000/callback
  ```
  ⚠️ **Important**: This must match exactly what we use in the OAuth helper

### Application Type

- Select: **"Server-based Applications"**
- This gives you `client_id` and `client_secret` (no `grant_token` initially)

## Step 3: Configure Scopes

In the **Scopes** section, add these permissions:

### Required Scopes:

- `ZohoCRM.modules.ALL` - Full access to CRM modules
- `ZohoCRM.settings.ALL` - Access to CRM settings
- `ZohoCRM.users.ALL` - Access to user information

### Optional Scopes (for advanced features):

- `ZohoCRM.org.ALL` - Organization settings
- `ZohoCRM.bulk.ALL` - Bulk operations

## Step 4: Save and Get Credentials

1. Click **"Create"** or **"Save"**
2. Copy your credentials:
   - **Client ID**: `1000.XXXXXXXXXXXXXXXXX`
   - **Client Secret**: `xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

## Step 5: Update Your Environment

Update your `.env` file:

```env
# Zoho CRM Configuration
ZOHO_CLIENT_ID=1000.YOUR_CLIENT_ID_HERE
ZOHO_CLIENT_SECRET=your_client_secret_here
ZOHO_REFRESH_TOKEN=  # Leave empty for now - we'll get this via OAuth
ZOHO_ENVIRONMENT=US  # Change to EU or IN if needed
```

## Step 6: Test the OAuth Flow

Now use the OAuth helper with the correct redirect URI:

```bash
# Generate authorization URL
python oauth_helper.py --get-auth-url --redirect-uri http://localhost:8000/callback
```

This should output a URL like:

```
https://accounts.zoho.com/oauth/v2/auth?response_type=code&client_id=1000.YOUR_CLIENT_ID&scope=ZohoCRM.modules.ALL%2CZohoCRM.settings.ALL%2CZohoCRM.users.ALL&redirect_uri=http%3A%2F%2Flocalhost%3A8000%2Fcallback&access_type=offline
```

## Step 7: Complete OAuth Authorization

1. **Copy the authorization URL** from Step 6
2. **Open it in your browser**
3. **Log in to your Zoho account** (if not already logged in)
4. **Review the permissions** being requested
5. **Click "Accept"** to grant permissions

## Step 8: Handle the Redirect

After clicking "Accept", you'll be redirected to:

```
http://localhost:8000/callback?code=1000.abc123def456...&location=us&accounts-server=https%3A%2F%2Faccounts.zoho.com
```

⚠️ **The page will show an error** - this is normal! We just need the `code` parameter.

### Extract the Authorization Code

From the URL in your browser's address bar, copy everything after `code=` and before the next `&`:

Example:

```
http://localhost:8000/callback?code=1000.abc123def456ghi789&location=us&...
```

Copy: `1000.abc123def456ghi789`

## Step 9: Exchange Code for Tokens

```bash
python oauth_helper.py --exchange-code 1000.abc123def456ghi789 --redirect-uri http://localhost:8000/callback
```

This should output:

```
✅ Success! Received tokens:
   Access Token: eyJhbGciOiJSUzI1NiIsInR5...
   Refresh Token: 1000.your_refresh_token_here
   Expires In: 3600 seconds

🧪 Testing refresh token...
✅ Token test successful! SDK initialized correctly.

📝 UPDATE YOUR .env FILE:
   ZOHO_REFRESH_TOKEN=1000.your_refresh_token_here
```

## Step 10: Update .env with Refresh Token

Add the refresh token to your `.env` file:

```env
ZOHO_REFRESH_TOKEN=1000.your_refresh_token_here
```

## Step 11: Test Your Setup

```bash
# Test the token directly
python oauth_helper.py --test-token 1000.your_refresh_token_here

# Or start the MCP server
python src/main.py
```

## Troubleshooting

### Issue: "Invalid Client" Error

**Solution**:

- Double-check your `client_id` and `client_secret` in `.env`
- Ensure they match exactly what's shown in Zoho Console

### Issue: "Redirect URI Mismatch" Error

**Solution**:

- In Zoho Console, verify **Authorized Redirect URIs** contains: `http://localhost:8000/callback`
- Use the exact same URI in the OAuth helper commands

### Issue: "Invalid Scope" Error

**Solution**:

- In Zoho Console, ensure you've added the required scopes:
  - `ZohoCRM.modules.ALL`
  - `ZohoCRM.settings.ALL`
  - `ZohoCRM.users.ALL`

### Issue: Browser Shows "This site can't be reached"

**This is normal!** We're not running a web server on localhost:8000. Just copy the `code` parameter from the URL.

### Issue: Code Expired

Authorization codes expire quickly (usually 10 minutes). If you get an "expired code" error:

1. Generate a new authorization URL
2. Go through the authorization process again
3. Quickly exchange the new code

## Environment-Specific Setup

### For EU Accounts (zoho.eu)

```env
ZOHO_ENVIRONMENT=EU
```

Authorization URL will use `https://accounts.zoho.eu`

### For India Accounts (zoho.in)

```env
ZOHO_ENVIRONMENT=IN
```

Authorization URL will use `https://accounts.zoho.in`

## Security Best Practices

1. **Never commit your refresh token** to version control
2. **Rotate your client secret** periodically in Zoho Console
3. **Use environment variables** for all sensitive data
4. **Limit scopes** to only what your application needs
5. **Monitor API usage** in Zoho Console

## Next Steps

Once you have a valid refresh token:

1. Your MCP server will start successfully
2. The Zoho SDK will automatically refresh access tokens
3. You can use all MCP tools and resources
4. The server will no longer run in "demo mode"

## Support

If you encounter issues:

1. Check the Zoho Console for any error messages
2. Verify your account has CRM access
3. Ensure your Zoho CRM organization is set up
4. Contact Zoho support if needed
