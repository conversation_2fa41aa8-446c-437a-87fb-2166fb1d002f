"""
Example script demonstrating how to use the Zoho CRM MCP Server authentication.

This script shows the complete OAuth 2.1 flow with PKCE for authenticating
with the Zoho CRM MCP server.
"""

import asyncio
import httpx
import json
from urllib.parse import urlparse, parse_qs


class ZohoMCPAuthExample:
    """Example client for Zoho MCP authentication."""

    def __init__(self, server_url: str = "http://localhost:8000"):
        self.server_url = server_url
        self.access_token = None

    async def get_authorization_url(self) -> dict:
        """Step 1: Get authorization URL from MCP server."""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.server_url}/auth/authorize")
            response.raise_for_status()
            return response.json()

    def simulate_user_authorization(self, auth_data: dict) -> str:
        """
        Step 2: Simulate user visiting authorization URL.

        In a real application, the user would visit the authorization_url
        in their browser and grant permissions. This would redirect them
        back with an authorization code.

        For this example, we'll simulate receiving an authorization code.
        """
        print(f"🌐 User should visit: {auth_data['authorization_url']}")
        print("📝 After granting permissions, you'll get an authorization code")

        # In a real scenario, you'd get this from the redirect
        # For demo purposes, we'll simulate it
        return "simulated_authorization_code"

    async def exchange_code_for_token(
        self,
        authorization_code: str,
        code_verifier: str,
        client_id: str,
        client_secret: str,
    ) -> dict:
        """
        Step 3: Exchange authorization code for access token.

        This would normally be done with Zoho's token endpoint,
        but for this example we'll simulate it.
        """
        # In a real implementation, you'd call Zoho's token endpoint:
        # POST https://accounts.zoho.com/oauth/v2/token

        print("🔄 Exchanging authorization code for access token...")
        print("   (In real implementation, this calls Zoho's token endpoint)")

        # Simulate successful token response
        return {
            "access_token": "simulated_access_token_12345",
            "token_type": "Bearer",
            "expires_in": 3600,
            "refresh_token": "simulated_refresh_token",
            "scope": "ZohoCRM.modules.ALL ZohoCRM.settings.ALL",
        }

    async def validate_token(self, access_token: str) -> dict:
        """Step 4: Validate token with MCP server."""
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {access_token}"}
            response = await client.post(
                f"{self.server_url}/auth/validate", headers=headers
            )
            response.raise_for_status()
            return response.json()

    async def make_authenticated_request(self, endpoint: str) -> dict:
        """Step 5: Make authenticated API request."""
        if not self.access_token:
            raise ValueError(
                "No access token available. Complete authentication first."
            )

        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = await client.post(
                f"{self.server_url}/{endpoint}",
                headers=headers,
                json={"module": "Leads", "fields": ["Last_Name", "Company"]},
            )

            if response.status_code == 401:
                print("❌ Authentication failed!")
                print("📄 Error response:")
                print(json.dumps(response.json(), indent=2))
                return response.json()

            response.raise_for_status()
            return response.json()

    async def get_auth_requirements(self) -> dict:
        """Get authentication requirements from server."""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.server_url}/auth/requirements")
            response.raise_for_status()
            return response.json()

    async def test_unauthenticated_request(self) -> dict:
        """Test making a request without authentication to see error response."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.server_url}/fetch_records", json={"module": "Leads"}
            )

            # Should return 401 with structured error
            return {"status_code": response.status_code, "response": response.json()}


async def main():
    """Run the complete authentication example."""
    print("🚀 Zoho CRM MCP Server Authentication Example")
    print("=" * 50)

    client = ZohoMCPAuthExample()

    try:
        # Test 1: Get authentication requirements
        print("\n📋 Step 1: Getting authentication requirements...")
        auth_requirements = await client.get_auth_requirements()
        print("✅ Authentication requirements:")
        print(json.dumps(auth_requirements, indent=2))

        # Test 2: Try unauthenticated request
        print("\n🚫 Step 2: Testing unauthenticated request...")
        unauth_result = await client.test_unauthenticated_request()
        print(f"Status Code: {unauth_result['status_code']}")
        print("Error Response:")
        print(json.dumps(unauth_result["response"], indent=2))

        # Test 3: Get authorization URL
        print("\n🔗 Step 3: Getting authorization URL...")
        auth_data = await client.get_authorization_url()
        print("✅ Authorization data received:")
        print(f"   State: {auth_data['state']}")
        print(f"   Code Verifier: {auth_data['code_verifier'][:20]}...")
        print(f"   Authorization URL: {auth_data['authorization_url'][:80]}...")

        # Test 4: Simulate OAuth flow
        print("\n👤 Step 4: Simulating user authorization...")
        auth_code = client.simulate_user_authorization(auth_data)
        print(f"   Simulated authorization code: {auth_code}")

        # Test 5: Exchange for token (simulated)
        print("\n🎫 Step 5: Exchanging code for token...")
        token_data = await client.exchange_code_for_token(
            authorization_code=auth_code,
            code_verifier=auth_data["code_verifier"],
            client_id="your_client_id",  # Would come from config
            client_secret="your_client_secret",  # Would come from config
        )
        print("✅ Token received:")
        print(f"   Access Token: {token_data['access_token'][:20]}...")
        print(f"   Expires In: {token_data['expires_in']} seconds")

        # Test 6: Validate token
        print("\n✅ Step 6: Validating token with server...")
        client.access_token = token_data["access_token"]

        # Note: This will fail in the example because we're using a simulated token
        # In a real implementation with actual Zoho tokens, this would work
        try:
            validation_result = await client.validate_token(token_data["access_token"])
            print("Token validation result:")
            print(json.dumps(validation_result, indent=2))
        except httpx.HTTPStatusError as e:
            print(f"⚠️  Token validation failed (expected with simulated token): {e}")

        # Test 7: Make authenticated request
        print("\n🔐 Step 7: Making authenticated API request...")
        try:
            api_result = await client.make_authenticated_request("fetch_records")
            print("✅ API request successful:")
            print(json.dumps(api_result, indent=2))
        except httpx.HTTPStatusError as e:
            print(f"⚠️  API request failed (expected with simulated token): {e}")

        print("\n🎉 Authentication example completed!")
        print("\n📝 Next Steps:")
        print("   1. Set up your Zoho OAuth app credentials")
        print("   2. Start the MCP server with AUTH_ENABLED=true")
        print("   3. Use real authorization codes from Zoho")
        print("   4. Make authenticated requests to the MCP server")

    except httpx.ConnectError:
        print("❌ Could not connect to MCP server.")
        print("   Make sure the server is running at http://localhost:8000")
        print("   Start it with: python src/main.py")
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
