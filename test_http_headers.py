#!/usr/bin/env python3
"""
Test script to verify Bear<PERSON> token extraction in standard MCP HTTP server.
"""

import asyncio
from mcp.client.streamable_http import streamablehttp_client
from mcp.client.session import ClientSession


async def test_http_headers():
    """Test HTTP headers with Bear<PERSON> token"""

    # Set up headers with Bear<PERSON> token
    headers = {"Authorization": "Bearer test-token-123"}

    print("🔗 Connecting to Zoho CRM MCP server: http://127.0.0.1:8000/")
    print("🔐 Using Bearer token authentication")

    try:
        async with streamablehttp_client("http://127.0.0.1:8000/", headers=headers) as (
            read,
            write,
            _,
        ):
            async with ClientSession(read, write) as session:
                print("✅ Connected to MCP server via HTTP")

                # Initialize the session
                await session.initialize()
                print("✅ Session initialized")

                # List available tools
                tools = await session.list_tools()
                print(f"📚 Available tools: {[tool.name for tool in tools.tools]}")

                # Try to call a tool that requires authentication
                if tools.tools:
                    tool_name = tools.tools[0].name
                    print(f"🔧 Testing tool: {tool_name}")

                    try:
                        # This should trigger the authentication check
                        if tool_name == "create_record":
                            result = await session.call_tool(
                                tool_name,
                                {
                                    "module": "Leads",
                                    "record_data": {
                                        "Last_Name": "Test",
                                        "Company": "Test Company",
                                    },
                                },
                            )
                        elif tool_name == "get_records":
                            result = await session.call_tool(
                                tool_name, {"module": "Leads"}
                            )
                        elif tool_name == "search_records":
                            result = await session.call_tool(
                                tool_name,
                                {
                                    "module": "Leads",
                                    "search_criteria": "(Last_Name:equals:Test)",
                                },
                            )
                        else:
                            result = await session.call_tool(tool_name, {})

                        print(f"✅ Tool result: {result}")
                    except Exception as e:
                        print(f"❌ Tool call error: {e}")

    except Exception as e:
        print(f"❌ Connection error: {e}")


if __name__ == "__main__":
    asyncio.run(test_http_headers())
