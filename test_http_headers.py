#!/usr/bin/env python3
"""
Test script to verify <PERSON><PERSON> token extraction in FastMCP HTTP server.
"""

import asyncio
from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport


async def test_http_headers():
    """Test HTTP headers with <PERSON><PERSON> token"""

    # Create transport with Authorization header
    transport = StreamableHttpTransport(
        url="http://127.0.0.1:8000/mcp/",
        headers={"Authorization": "Bearer test-token-123"},
    )

    client = Client(transport)

    try:
        async with client:
            print("Connected to FastMCP server via HTTP")

            # List available tools
            tools = await client.list_tools()
            print(f"Available tools: {[tool.name for tool in tools]}")

            # Try to call a tool that requires authentication
            if tools:
                tool_name = tools[0].name
                print(f"Testing tool: {tool_name}")

                try:
                    # This should trigger the authentication check
                    if tool_name == "create_record":
                        result = await client.call_tool(
                            tool_name,
                            {
                                "module": "Leads",
                                "record_data": {
                                    "Last_Name": "Test",
                                    "Company": "Test Company",
                                },
                            },
                        )
                    elif tool_name == "get_records":
                        result = await client.call_tool(tool_name, {"module": "Leads"})
                    elif tool_name == "search_records":
                        result = await client.call_tool(
                            tool_name, {"module": "Leads", "criteria": "Last_Name:Test"}
                        )
                    else:
                        result = await client.call_tool(tool_name, {})

                    print(f"Tool result: {result}")
                except Exception as e:
                    print(f"Tool call error: {e}")

    except Exception as e:
        print(f"Connection error: {e}")


if __name__ == "__main__":
    asyncio.run(test_http_headers())
