#!/usr/bin/env python3
"""
Debug OAuth configuration and flow
"""
import urllib.parse
from src.config import Settings
from src.auth.zoho_provider import <PERSON>oh<PERSON>OAuthProvider


def debug_oauth():
    """Debug OAuth configuration"""

    print("🔍 OAuth Configuration Debug")
    print("=" * 50)

    # Load settings
    settings = Settings()

    print(f"Config redirect URI: {settings.oauth_redirect_uri}")
    print(f"Client ID: {settings.zoho_client_id}")
    print(f"Environment: {settings.zoho_environment}")
    print()

    # Initialize provider
    provider = ZohoOAuthProvider(
        client_id=settings.zoho_client_id,
        client_secret=settings.zoho_client_secret,
        environment=settings.zoho_environment,
        redirect_uri=settings.oauth_redirect_uri,
    )

    print(f"Provider redirect URI: {provider.redirect_uri}")
    print()

    # Generate auth request
    auth_request = provider.create_authorization_request()

    # Parse the authorization URL to see what redirect_uri is actually used
    parsed_url = urllib.parse.urlparse(auth_request["authorization_url"])
    query_params = urllib.parse.parse_qs(parsed_url.query)

    print("📋 Authorization URL Parameters:")
    for key, value in query_params.items():
        print(f"   {key}: {value[0] if len(value) == 1 else value}")

    print()
    print("🔑 PKCE Parameters:")
    print(f"   Code Verifier: {auth_request['code_verifier']}")
    print(f"   Code Challenge: {auth_request['code_challenge']}")
    print(f"   State: {auth_request['state']}")

    print()
    print("⚠️  IMPORTANT: Make sure your Zoho OAuth app has this EXACT redirect URI:")
    print(f"   {query_params['redirect_uri'][0]}")

    print()
    print("📝 Steps to fix 'invalid_grant' error:")
    print("1. Go to https://api-console.zoho.com/")
    print("2. Find your OAuth app")
    print("3. Edit the app settings")
    print(f"4. Set redirect URI to: {query_params['redirect_uri'][0]}")
    print("5. Save the changes")
    print("6. Get a fresh authorization code using the URL above")
    print("7. Use the EXACT code_verifier shown above in token exchange")


if __name__ == "__main__":
    debug_oauth()
