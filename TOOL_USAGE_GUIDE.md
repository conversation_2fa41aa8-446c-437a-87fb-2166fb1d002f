# Zoho CRM MCP Server - Tool Usage Guide

## 🎉 Enhanced Tool Descriptions & New get_tool_info Tool

The Zoho CRM MCP server now includes **detailed tool descriptions** with examples and a new **`get_tool_info`** tool that provides comprehensive parameter information for all available tools.

## 📚 Available Tools

### 1. **create_record** - Create New CRM Records
Create a new record in any Zoho CRM module with detailed field requirements.

**Required Parameters:**
- `module`: CRM module name
- `record_data`: Object with field names and values

**Module-Specific Required Fields:**
- **Leads**: `Last_Name`, `Company`
- **Contacts**: `Last_Name`
- **Accounts**: `Account_Name`
- **Deals**: `Deal_Name`, `Stage`, `Closing_Date`

**Example:**
```json
{
  "module": "Leads",
  "record_data": {
    "Last_Name": "Smith",
    "Company": "Acme Corp",
    "Email": "<EMAIL>",
    "Phone": "******-0123",
    "Lead_Source": "Website"
  }
}
```

### 2. **get_records** - Retrieve CRM Records
Retrieve records with pagination, field selection, and sorting.

**Required Parameters:**
- `module`: CRM module name

**Optional Parameters:**
- `fields`: Array of specific fields to retrieve
- `page`: Page number (default: 1)
- `per_page`: Records per page (default: 50, max: 200)

**Common Fields by Module:**
- **Leads**: `Last_Name`, `Company`, `Email`, `Phone`, `Lead_Source`, `Created_Time`
- **Contacts**: `Last_Name`, `First_Name`, `Email`, `Phone`, `Account_Name`
- **Accounts**: `Account_Name`, `Website`, `Industry`, `Annual_Revenue`
- **Deals**: `Deal_Name`, `Amount`, `Stage`, `Closing_Date`, `Account_Name`

**Example:**
```json
{
  "module": "Leads",
  "fields": ["Last_Name", "Company", "Email", "Phone", "Created_Time"],
  "page": 1,
  "per_page": 50
}
```

### 3. **search_records** - Advanced Record Search
Search records using powerful criteria with multiple operators.

**Required Parameters:**
- `module`: CRM module name
- `search_criteria`: Search criteria in Zoho format

**Optional Parameters:**
- `page`: Page number (default: 1)
- `per_page`: Records per page (default: 200, max: 200)

**Search Operators:**
- `equals`: Exact match
- `contains`: Partial match (case-insensitive)
- `starts_with`: Begins with value
- `ends_with`: Ends with value
- `less_than`: Numeric/date comparison
- `greater_than`: Numeric/date comparison

**Search Criteria Examples:**
- Single condition: `"(Last_Name:equals:Smith)"`
- Multiple conditions: `"((Last_Name:equals:Smith)and(Company:contains:Tech))"`
- Date range: `"(Created_Time:greater_than:2024-01-01T00:00:00Z)"`
- Email domain: `"(Email:contains:@gmail.com)"`

**Example:**
```json
{
  "module": "Leads",
  "search_criteria": "((Last_Name:contains:Smith)and(Lead_Source:equals:Website))",
  "page": 1,
  "per_page": 100
}
```

### 4. **health_check** - Server Health Status
Check server health and configuration.

**Parameters:** None required

**Returns:**
- Server status and uptime
- Zoho API connectivity
- Authentication status
- Available modules and permissions

**Example:**
```json
{}
```

### 5. **get_tool_info** - 🆕 Tool Information Helper
Get detailed information about available tools and their parameters.

**Optional Parameters:**
- `tool_name`: Specific tool name (if not provided, returns info for all tools)

**Features:**
- Complete parameter specifications
- Required vs optional field information
- Module-specific field requirements
- Usage examples and best practices

**Examples:**

Get info for specific tool:
```json
{
  "tool_name": "create_record"
}
```

Get info for all tools:
```json
{}
```

## 🚀 How to Use get_tool_info

### Example 1: Get Information for All Tools
```python
result = await session.call_tool("get_tool_info", {})
```

**Response includes:**
- Summary of all available tools
- Complete specifications for each tool
- Parameter details and examples

### Example 2: Get Information for Specific Tool
```python
result = await session.call_tool("get_tool_info", {"tool_name": "create_record"})
```

**Response includes:**
- Tool description and purpose
- Required and optional parameters
- Module-specific field requirements
- Usage examples

## 📡 Postman Usage Examples

### List Tools with Enhanced Descriptions
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/list",
  "params": {}
}
```

### Get Detailed Tool Information
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "tools/call",
  "params": {
    "name": "get_tool_info",
    "arguments": {
      "tool_name": "create_record"
    }
  }
}
```

### Create a Lead with Required Fields
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "method": "tools/call",
  "params": {
    "name": "create_record",
    "arguments": {
      "module": "Leads",
      "record_data": {
        "Last_Name": "Johnson",
        "Company": "Tech Solutions Inc",
        "Email": "<EMAIL>",
        "Phone": "******-0199",
        "Lead_Source": "Cold Call"
      }
    }
  }
}
```

### Search for Recent Leads
```json
{
  "jsonrpc": "2.0",
  "id": 4,
  "method": "tools/call",
  "params": {
    "name": "search_records",
    "arguments": {
      "module": "Leads",
      "search_criteria": "(Created_Time:greater_than:2024-01-01T00:00:00Z)",
      "page": 1,
      "per_page": 50
    }
  }
}
```

## 🎯 Key Improvements

1. **Enhanced Tool Descriptions**: Each tool now includes detailed descriptions with examples, required fields, and usage patterns.

2. **New get_tool_info Tool**: Provides comprehensive parameter information and examples for all tools.

3. **Module-Specific Requirements**: Clear documentation of required fields for each CRM module.

4. **Search Operator Documentation**: Complete list of available search operators with examples.

5. **Field Selection Guidance**: Common field recommendations for each module type.

6. **Validation and Error Handling**: Better parameter validation with clear error messages.

The enhanced tools make it much easier to understand what parameters are required and how to use each tool effectively!
