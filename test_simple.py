#!/usr/bin/env python3
"""
Test script for the simplified Zoho CRM MCP Server
Demonstrates the clean REST API implementation
"""

import asyncio
import os
from dotenv import load_dotenv
from src.simple_main import ZohoAPI, ZohoSettings


async def test_simple_api():
    """Test the simplified API implementation"""
    load_dotenv()

    # Get access token from environment
    access_token = os.getenv("ZOHO_ACCESS_TOKEN")
    if not access_token:
        print("❌ ZOHO_ACCESS_TOKEN not found in environment")
        print("💡 Run: python simple_oauth.py to get tokens")
        return

    # Initialize settings and API
    settings = ZohoSettings()
    api = ZohoAPI(settings)

    print("🧪 Testing Simplified Zoho CRM API")
    print(f"🌍 Environment: {settings.zoho_environment}")
    print(f"🔗 Base URL: {api.base_url}")
    print(f"🔑 Token: {access_token[:10]}...")

    # Test 1: Get records
    print("\n📋 Test 1: Getting Leads...")
    try:
        result = await api.get_records(
            "Leads", access_token, fields=["Last_Name", "Company", "Email"], per_page=5
        )
        if result["success"]:
            print(f"✅ Success: Found {result['count']} records")
            for i, record in enumerate(result["records"][:2], 1):
                print(
                    f"   {i}. {record.get('Last_Name', 'N/A')} - {record.get('Company', 'N/A')}"
                )
        else:
            print(f"❌ Failed: {result['message']}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test 2: Search records
    print("\n🔍 Test 2: Searching Leads...")
    try:
        result = await api.search_records(
            "Leads", "(Last_Name:starts_with:A)", access_token
        )
        if result["success"]:
            print(f"✅ Success: Found {result['count']} matching records")
        else:
            print(f"❌ Failed: {result['message']}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test 3: Create record (commented out to avoid creating test data)
    print("\n➕ Test 3: Create Record (dry run)")
    test_data = {
        "Last_Name": "Test User",
        "Company": "Test Company",
        "Email": "<EMAIL>",
    }
    print(f"   Would create: {test_data}")
    print("   (Uncomment in code to actually create)")

    # Uncomment to actually test record creation:
    # try:
    #     result = await api.create_record("Leads", test_data, access_token)
    #     if result["success"]:
    #         print(f"✅ Success: Created record with ID {result['id']}")
    #     else:
    #         print(f"❌ Failed: {result['message']}")
    # except Exception as e:
    #     print(f"❌ Error: {e}")

    print("\n🎉 Testing completed!")


if __name__ == "__main__":
    asyncio.run(test_simple_api())
