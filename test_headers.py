#!/usr/bin/env python3
"""
Test script to verify <PERSON><PERSON> token extraction in FastMCP server
"""

import asyncio
import json
import subprocess
import sys
from typing import Dict, Any


async def test_mcp_server_with_headers():
    """Test the MCP server with Bear<PERSON> token in headers"""
    
    # Start the server process
    server_process = subprocess.Popen(
        [sys.executable, "src/main.py", "--stdio"],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        cwd="/Users/<USER>/Desktop/Ruh/zoho-mcp"
    )
    
    try:
        # Send initialize request
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        print("📤 Sending initialize request...")
        server_process.stdin.write(json.dumps(init_request) + "\n")
        server_process.stdin.flush()
        
        # Read response
        response_line = server_process.stdout.readline()
        print(f"📥 Initialize response: {response_line.strip()}")
        
        # Send tools/list request to trigger authentication
        tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list",
            "params": {}
        }
        
        print("📤 Sending tools/list request...")
        server_process.stdin.write(json.dumps(tools_request) + "\n")
        server_process.stdin.flush()
        
        # Read response
        response_line = server_process.stdout.readline()
        print(f"📥 Tools list response: {response_line.strip()}")
        
        # Test create_record tool without authentication
        create_request = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": "create_record",
                "arguments": {
                    "module": "Leads",
                    "record_data": {"Last_Name": "Test", "Company": "Test Corp"}
                }
            }
        }
        
        print("📤 Sending create_record request (no auth)...")
        server_process.stdin.write(json.dumps(create_request) + "\n")
        server_process.stdin.flush()
        
        # Read response
        response_line = server_process.stdout.readline()
        print(f"📥 Create record response: {response_line.strip()}")
        
        # Parse and check if we get the authentication error
        try:
            response_data = json.loads(response_line)
            if "result" in response_data and "error" in response_data["result"]:
                error_data = response_data["result"]["error"]
                if error_data.get("code") == "AUTHENTICATION_REQUIRED":
                    print("✅ Authentication error response is correctly formatted!")
                    print(f"   Provider: {error_data['details']['authentication_requirements'][0]['provider']}")
                    print(f"   Auth type: {error_data['details']['authentication_requirements'][0]['auth_type']}")
                    print(f"   Header format: {error_data['details']['authentication_requirements'][0]['header_format']}")
                else:
                    print("❌ Authentication error format is incorrect")
            else:
                print("❌ Expected authentication error but got different response")
        except json.JSONDecodeError:
            print("❌ Could not parse response as JSON")
        
    finally:
        # Clean up
        server_process.terminate()
        server_process.wait()


if __name__ == "__main__":
    print("🧪 Testing Zoho MCP Server Authentication...")
    asyncio.run(test_mcp_server_with_headers())
