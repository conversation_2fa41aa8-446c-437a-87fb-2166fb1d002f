{"SDK-MODULE-METADATA": {"leads": {"api_name": "Leads", "generated_type": "default"}, "contacts": {"api_name": "Contacts", "generated_type": "default"}, "accounts": {"api_name": "Accounts", "generated_type": "default"}, "deals": {"api_name": "Deals", "generated_type": "default"}, "tasks": {"api_name": "Tasks", "generated_type": "default"}, "events": {"api_name": "Events", "generated_type": "default"}, "calls": {"api_name": "Calls", "generated_type": "default"}, "products": {"api_name": "Products", "generated_type": "default"}, "quotes": {"api_name": "Quotes", "generated_type": "default"}, "sales_orders": {"api_name": "Sales_Orders", "generated_type": "default"}, "purchase_orders": {"api_name": "Purchase_Orders", "generated_type": "default"}, "invoices": {"api_name": "Invoices", "generated_type": "default"}, "campaigns": {"api_name": "Campaigns", "generated_type": "default"}, "vendors": {"api_name": "Vend<PERSON>", "generated_type": "default"}, "price_books": {"api_name": "Price_Books", "generated_type": "default"}, "cases": {"api_name": "Cases", "generated_type": "default"}, "solutions": {"api_name": "Solutions", "generated_type": "default"}, "visits": {"api_name": "Visits", "generated_type": "default"}, "quoted_items": {"api_name": "Quoted_Items", "generated_type": "subform"}, "dealhistory": {"api_name": "DealHistory", "generated_type": "field_tracker"}, "email_sentiment": {"api_name": "Email_Sentiment", "generated_type": "default"}, "ordered_items": {"api_name": "Ordered_Items", "generated_type": "subform"}, "email_analytics": {"api_name": "Email_Analytics", "generated_type": "default"}, "email_template_analytics": {"api_name": "Email_Template_Analytics", "generated_type": "default"}, "purchase_items": {"api_name": "Purchase_Items", "generated_type": "subform"}, "invoiced_items": {"api_name": "Invoiced_Items", "generated_type": "subform"}, "notes": {"api_name": "Notes", "generated_type": "default"}, "attachments": {"api_name": "Attachments", "generated_type": "default"}, "actions_performed": {"api_name": "Actions_Performed", "generated_type": "default"}, "locking_information__s": {"api_name": "Locking_Information__s", "generated_type": "default"}}, "FIELDS-LAST-MODIFIED-TIME": 1751527175880.3398, "leads": {"Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Company": {"type": "String", "name": "Company"}, "First_Name": {"type": "String", "name": "First_Name"}, "Last_Name": {"required": true, "type": "String", "name": "Last_Name"}, "Designation": {"type": "String", "name": "Designation"}, "Email": {"type": "String", "name": "Email"}, "Phone": {"type": "String", "name": "Phone"}, "Fax": {"type": "String", "name": "Fax"}, "Mobile": {"type": "String", "name": "Mobile"}, "Website": {"type": "String", "name": "Website"}, "Lead_Source": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Advertisement", "Cold Call", "Employee Referral", "External Referral", "Online Store", "X (Twitter)", "Facebook", "Partner", "Public Relations", "Sales Email Alias", "Seminar Partner", "Internal Seminar", "Trade Show", "Web Download", "Web Research", "Cha<PERSON>"], "name": "Lead_Source"}, "Lead_Status": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Attempted to Contact", "Contact in Future", "Contacted", "Junk Lead", "Lost Lead", "Not Contacted", "Pre-Qualified", "Not Qualified"], "name": "Lead_Status"}, "Industry": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "ASP (Application Service Provider)", "Data/Telecom OEM", "ERP (Enterprise Resource Planning)", "Government/Military", "Large Enterprise", "ManagementISV", "MSP (Management Service Provider)", "Network Equipment Enterprise", "Non-management ISV", "Optical Networking", "Service Provider", "Small/Medium Enterprise", "Storage Equipment", "Storage Service Provider", "Systems Integrator", "Wireless Industry", "ERP", "Management ISV"], "name": "Industry"}, "No_of_Employees": {"type": "Integer", "name": "No_of_Employees"}, "Annual_Revenue": {"type": "Float", "name": "Annual_Revenue"}, "Rating": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Acquired", "Active", "Market Failed", "Project Cancelled", "Shut Down"], "name": "Rating"}, "Full_Name": {"type": "String", "name": "Full_Name"}, "Street": {"type": "String", "name": "Street"}, "City": {"type": "String", "name": "City"}, "State": {"type": "String", "name": "State"}, "Zip_Code": {"type": "String", "name": "Zip_Code"}, "Country": {"type": "String", "name": "Country"}, "Description": {"type": "String", "name": "Description"}, "Skype_ID": {"type": "String", "name": "Skype_ID"}, "Email_Opt_Out": {"type": "Boolean", "name": "Email_Opt_Out"}, "Salutation": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Mr.", "Mrs.", "Ms.", "Dr.", "Prof."], "name": "Salutation"}, "Secondary_Email": {"type": "String", "name": "Secondary_Email"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}, "Twitter": {"type": "String", "name": "Twitter"}, "Record_Image": {"type": "String", "name": "Record_Image"}, "Converted_Date_Time": {"type": "DateTime", "name": "Converted_Date_Time"}, "Lead_Conversion_Time": {"type": "Integer", "name": "Lead_Conversion_Time"}, "Unsubscribed_Mode": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Consent form", "Manual", "Unsubscribe link", "Zoho campaigns"], "name": "Unsubscribed_Mode"}, "Unsubscribed_Time": {"type": "DateTime", "name": "Unsubscribed_Time"}, "Converted_Account": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Accounts", "skip-mandatory": true, "name": "Converted_Account"}, "Converted_Contact": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Contacts", "name": "Converted_Contact"}, "Converted_Deal": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Deals", "name": "Converted_Deal"}, "id": {"type": "Integer", "name": "id"}, "Change_Log_Time__s": {"type": "DateTime", "name": "Change_Log_Time__s"}, "Converted__s": {"type": "Boolean", "name": "Converted__s"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Last_Enriched_Time__s": {"type": "DateTime", "name": "Last_Enriched_Time__s"}, "Enrich_Status__s": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Available", "Enriched", "Data not found"], "name": "Enrich_Status__s"}}, "accounts": {"Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Rating": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Acquired", "Active", "Market Failed", "Project Cancelled", "Shut Down"], "name": "Rating"}, "Account_Name": {"required": true, "type": "String", "name": "Account_Name"}, "Phone": {"type": "String", "name": "Phone"}, "Account_Site": {"type": "String", "name": "Account_Site"}, "Fax": {"type": "String", "name": "Fax"}, "Parent_Account": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Accounts", "skip-mandatory": true, "name": "Parent_Account"}, "Website": {"type": "String", "name": "Website"}, "Account_Number": {"type": "Integer", "name": "Account_Number"}, "Ticker_Symbol": {"type": "String", "name": "Ticker_Symbol"}, "Account_Type": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Analyst", "Competitor", "Customer", "Distributor", "Integrator", "Investor", "Other", "Partner", "Press", "Prospect", "Reseller", "Supplier", "<PERSON><PERSON><PERSON>"], "name": "Account_Type"}, "Ownership": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Other", "Private", "Public", "Subsidiary", "Partnership", "Government", "Privately Held", "Public Company"], "name": "Ownership"}, "Industry": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "ASP (Application Service Provider)", "Data/Telecom OEM", "ERP (Enterprise Resource Planning)", "Government/Military", "Large Enterprise", "ManagementISV", "MSP (Management Service Provider)", "Network Equipment Enterprise", "Non-management ISV", "Optical Networking", "Service Provider", "Small/Medium Enterprise", "Storage Equipment", "Storage Service Provider", "Systems Integrator", "Wireless Industry", "Financial Services", "Education", "Technology", "Real Estate", "Consulting", "Communications", "Manufacturing"], "name": "Industry"}, "Employees": {"type": "Integer", "name": "Employees"}, "Annual_Revenue": {"type": "Float", "name": "Annual_Revenue"}, "SIC_Code": {"type": "Integer", "name": "SIC_Code"}, "Billing_Street": {"type": "String", "name": "Billing_Street"}, "Shipping_Street": {"type": "String", "name": "Shipping_Street"}, "Billing_City": {"type": "String", "name": "Billing_City"}, "Shipping_City": {"type": "String", "name": "Shipping_City"}, "Billing_State": {"type": "String", "name": "Billing_State"}, "Shipping_State": {"type": "String", "name": "Shipping_State"}, "Billing_Code": {"type": "String", "name": "Billing_Code"}, "Shipping_Code": {"type": "String", "name": "Shipping_Code"}, "Billing_Country": {"type": "String", "name": "Billing_Country"}, "Shipping_Country": {"type": "String", "name": "Shipping_Country"}, "Description": {"type": "String", "name": "Description"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}, "Record_Image": {"type": "String", "name": "Record_Image"}, "id": {"type": "Integer", "name": "id"}, "Change_Log_Time__s": {"type": "DateTime", "name": "Change_Log_Time__s"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Last_Enriched_Time__s": {"type": "DateTime", "name": "Last_Enriched_Time__s"}, "Enrich_Status__s": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Available", "Enriched", "Data not found"], "name": "Enrich_Status__s"}}, "contacts": {"Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Lead_Source": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Advertisement", "Cold Call", "Employee Referral", "External Referral", "Online Store", "Partner", "X (Twitter)", "Public Relations", "Facebook", "Sales Email Alias", "Seminar Partner", "Internal Seminar", "Trade Show", "Web Download", "Web Research", "Web Cases", "Web Mail", "Cha<PERSON>"], "name": "Lead_Source"}, "First_Name": {"type": "String", "name": "First_Name"}, "Last_Name": {"required": true, "type": "String", "name": "Last_Name"}, "Account_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Accounts", "skip-mandatory": true, "name": "Account_Name"}, "Vendor_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Vend<PERSON>", "name": "Vendor_Name"}, "Email": {"type": "String", "name": "Email"}, "Title": {"type": "String", "name": "Title"}, "Department": {"type": "String", "name": "Department"}, "Phone": {"type": "String", "name": "Phone"}, "Home_Phone": {"type": "String", "name": "Home_Phone"}, "Other_Phone": {"type": "String", "name": "Other_Phone"}, "Fax": {"type": "String", "name": "Fax"}, "Mobile": {"type": "String", "name": "Mobile"}, "Date_of_Birth": {"type": "Date", "name": "Date_of_Birth"}, "Assistant": {"type": "String", "name": "Assistant"}, "Asst_Phone": {"type": "String", "name": "Asst_Phone"}, "Full_Name": {"type": "String", "name": "Full_Name"}, "Mailing_Street": {"type": "String", "name": "Mailing_Street"}, "Other_Street": {"type": "String", "name": "Other_Street"}, "Mailing_City": {"type": "String", "name": "Mailing_City"}, "Other_City": {"type": "String", "name": "Other_City"}, "Mailing_State": {"type": "String", "name": "Mailing_State"}, "Other_State": {"type": "String", "name": "Other_State"}, "Mailing_Zip": {"type": "String", "name": "Mailing_Zip"}, "Other_Zip": {"type": "String", "name": "Other_Zip"}, "Mailing_Country": {"type": "String", "name": "Mailing_Country"}, "Other_Country": {"type": "String", "name": "Other_Country"}, "Description": {"type": "String", "name": "Description"}, "Email_Opt_Out": {"type": "Boolean", "name": "Email_Opt_Out"}, "Skype_ID": {"type": "String", "name": "Skype_ID"}, "Salutation": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Mr.", "Mrs.", "Ms.", "Dr.", "Prof."], "name": "Salutation"}, "Secondary_Email": {"type": "String", "name": "Secondary_Email"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}, "Twitter": {"type": "String", "name": "Twitter"}, "Record_Image": {"type": "String", "name": "Record_Image"}, "Reporting_To": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Contacts", "name": "Reporting_To"}, "Unsubscribed_Mode": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Consent form", "Manual", "Unsubscribe link", "Zoho campaigns"], "name": "Unsubscribed_Mode"}, "Unsubscribed_Time": {"type": "DateTime", "name": "Unsubscribed_Time"}, "id": {"type": "Integer", "name": "id"}, "Change_Log_Time__s": {"type": "DateTime", "name": "Change_Log_Time__s"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Last_Enriched_Time__s": {"type": "DateTime", "name": "Last_Enriched_Time__s"}, "Enrich_Status__s": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Available", "Enriched", "Data not found"], "name": "Enrich_Status__s"}}, "vendors": {"Vendor_Name": {"required": true, "type": "String", "name": "Vendor_Name"}, "Phone": {"type": "String", "name": "Phone"}, "Email": {"type": "String", "name": "Email"}, "Website": {"type": "String", "name": "Website"}, "GL_Account": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Sales-Software", "Sales-Hardware", "Rental Income", "Interest Income", "Sales Software Support", "Sales Other", "Interest Sales", "Labor Hardware Service"], "name": "GL_Account"}, "Category": {"type": "String", "name": "Category"}, "Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Street": {"type": "String", "name": "Street"}, "City": {"type": "String", "name": "City"}, "State": {"type": "String", "name": "State"}, "Zip_Code": {"type": "String", "name": "Zip_Code"}, "Country": {"type": "String", "name": "Country"}, "Description": {"type": "String", "name": "Description"}, "Record_Image": {"type": "String", "name": "Record_Image"}, "id": {"type": "Integer", "name": "id"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Email_Opt_Out": {"type": "Boolean", "name": "Email_Opt_Out"}, "Unsubscribed_Mode": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Consent form", "Manual", "Unsubscribe link", "Zoho campaigns"], "name": "Unsubscribed_Mode"}, "Unsubscribed_Time": {"type": "DateTime", "name": "Unsubscribed_Time"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}}, "deals": {"Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Amount": {"type": "Float", "name": "Amount"}, "Deal_Name": {"required": true, "type": "String", "name": "Deal_Name"}, "Closing_Date": {"type": "Date", "name": "Closing_Date"}, "Account_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Accounts", "skip-mandatory": true, "name": "Account_Name"}, "Stage": {"required": true, "type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Qualification", "Needs Analysis", "Value Proposition", "Identify Decision Makers", "Proposal/Price Quote", "Negotiation/Review", "Closed Won", "Closed Lost", "Closed Lost to Competition"], "name": "Stage"}, "Type": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Existing Business", "New Business"], "name": "Type"}, "Probability": {"type": "Integer", "name": "Probability"}, "Next_Step": {"type": "String", "name": "Next_Step"}, "Lead_Source": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Advertisement", "Cold Call", "Employee Referral", "External Referral", "Online Store", "Partner", "Public Relations", "Sales Email Alias", "Seminar Partner", "Internal Seminar", "Trade Show", "Web Download", "Web Research", "Cha<PERSON>"], "name": "Lead_Source"}, "Campaign_Source": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Campaigns", "name": "Campaign_Source"}, "Description": {"type": "String", "name": "Description"}, "Contact_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Contacts", "name": "Contact_Name"}, "Expected_Revenue": {"type": "Float", "name": "Expected_Revenue"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}, "Lead_Conversion_Time": {"type": "Integer", "name": "Lead_Conversion_Time"}, "Sales_Cycle_Duration": {"type": "Integer", "name": "Sales_Cycle_Duration"}, "Overall_Sales_Duration": {"type": "Integer", "name": "Overall_Sales_Duration"}, "id": {"type": "Integer", "name": "id"}, "Change_Log_Time__s": {"type": "DateTime", "name": "Change_Log_Time__s"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Reason_For_Loss__s": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Expectation Mismatch", "Price", "Unqualified Customer", "Lack of response", "Missed Follow Ups", "Wrong Target", "Competition", "Future Interest", "Other"], "name": "Reason_For_Loss__s"}}, "campaigns": {"Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Type": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Conference", "Webinar", "Trade Show", "Public Relations", "Partners", "Referral Program", "Advertisement", "Banner Ads", "Direct mail", "Email", "Telemarketing", "Others"], "name": "Type"}, "Campaign_Name": {"required": true, "type": "String", "name": "Campaign_Name"}, "Status": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Planning", "Active", "Inactive", "Complete"], "name": "Status"}, "Start_Date": {"type": "Date", "name": "Start_Date"}, "End_Date": {"type": "Date", "name": "End_Date"}, "Expected_Revenue": {"type": "Float", "name": "Expected_Revenue"}, "Budgeted_Cost": {"type": "Float", "name": "Budgeted_Cost"}, "Actual_Cost": {"type": "Float", "name": "Actual_Cost"}, "Expected_Response": {"type": "Integer", "name": "Expected_Response"}, "Num_sent": {"type": "Integer", "name": "Num_sent"}, "Description": {"type": "String", "name": "Description"}, "Parent_Campaign": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Campaigns", "name": "Parent_Campaign"}, "id": {"type": "Integer", "name": "id"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}}, "tasks": {"Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Subject": {"required": true, "type": "String", "name": "Subject"}, "Due_Date": {"type": "Date", "name": "Due_Date"}, "Who_Id": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Contacts", "name": "Who_Id"}, "What_Id": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "name": "What_Id"}, "Status": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Not Started", "Deferred", "In Progress", "Completed", "Waiting for input"], "name": "Status"}, "Priority": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["High", "Highest", "Low", "Lowest", "Normal"], "name": "Priority"}, "Description": {"type": "String", "name": "Description"}, "Send_Notification_Email": {"type": "Boolean", "name": "Send_Notification_Email"}, "Remind_At": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.RemindAt", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.RemindAt", "name": "Remind_At"}, "Recurring_Activity": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.RecurringActivity", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.RecurringActivity", "name": "Recurring_Activity"}, "Closed_Time": {"type": "DateTime", "name": "Closed_Time"}, "id": {"type": "Integer", "name": "id"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}}, "events": {"Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Event_Title": {"required": true, "type": "String", "name": "Event_Title"}, "Venue": {"type": "String", "name": "Venue"}, "Who_Id": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Contacts", "name": "Who_Id"}, "What_Id": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "name": "What_Id"}, "Description": {"type": "String", "name": "Description"}, "Recurring_Activity": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.RecurringActivity", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.RecurringActivity", "name": "Recurring_Activity"}, "Remind_At": {"type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Reminder", "name": "Remind_At"}, "All_day": {"type": "Boolean", "name": "All_day"}, "Participants": {"name": "Participants", "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Participants", "skip-mandatory": true}, "Check_In_Time": {"type": "DateTime", "name": "Check_In_Time"}, "Check_In_By": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Check_In_By"}, "Check_In_Comment": {"type": "String", "name": "Check_In_Comment"}, "Check_In_Sub_Locality": {"type": "String", "name": "Check_In_Sub_Locality"}, "Check_In_City": {"type": "String", "name": "Check_In_City"}, "Check_In_State": {"type": "String", "name": "Check_In_State"}, "Check_In_Country": {"type": "String", "name": "Check_In_Country"}, "Latitude": {"type": "Float", "name": "Latitude"}, "Longitude": {"type": "Float", "name": "Longitude"}, "ZIP_Code": {"type": "String", "name": "ZIP_Code"}, "Check_In_Address": {"type": "String", "name": "Check_In_Address"}, "Check_In_Status": {"type": "String", "name": "Check_In_Status"}, "Start_DateTime": {"required": true, "type": "DateTime", "name": "Start_DateTime"}, "End_DateTime": {"required": true, "type": "DateTime", "name": "End_DateTime"}, "Remind_Participants": {"type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Reminder", "name": "Remind_Participants"}, "id": {"type": "Integer", "name": "id"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}, "Meeting_Venue__s": {"required": true, "type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["In-office", "Client location", "Online"], "name": "Meeting_Venue__s"}, "Meeting_Provider__s": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "name": "Meeting_Provider__s"}}, "calls": {"Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Subject": {"type": "String", "name": "Subject"}, "Call_Type": {"required": true, "type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Outbound", "Inbound", "Missed"], "name": "Call_Type"}, "Call_Purpose": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Prospecting", "Administrative", "Negotiation", "Demo", "Project", "Desk"], "name": "Call_Purpose"}, "Who_Id": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Contacts", "name": "Who_Id"}, "What_Id": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "name": "What_Id"}, "Call_Start_Time": {"required": true, "type": "DateTime", "name": "Call_Start_Time"}, "Call_Duration": {"type": "String", "name": "Call_Duration"}, "Call_Duration_in_seconds": {"type": "Integer", "name": "Call_Duration_in_seconds"}, "Description": {"type": "String", "name": "Description"}, "Call_Result": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Interested", "Not interested", "No response/Busy", "Requested more info", "Requested call back", "Invalid number"], "name": "Call_Result"}, "CTI_Entry": {"type": "Boolean", "name": "CTI_Entry"}, "Reminder": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["None", "5 minutes before", "10 minutes before", "15 minutes before", "30 minutes before", "1 hour before", "2 hours before", "1 day before", "2 days before"], "name": "Reminder"}, "Outgoing_Call_Status": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Scheduled", "Completed", "Overdue", "Cancelled"], "name": "Outgoing_Call_Status"}, "Scheduled_In_CRM": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["False", "True"], "name": "Scheduled_In_CRM"}, "id": {"type": "Integer", "name": "id"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}, "Call_Agenda": {"type": "String", "name": "Call_Agenda"}, "Caller_ID": {"type": "String", "name": "Caller_ID"}, "Dialled_Number": {"type": "String", "name": "Dialled_Number"}, "Voice_Recording__s": {"type": "String", "name": "Voice_Recording__s"}}, "products": {"Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Product_Name": {"required": true, "type": "String", "name": "Product_Name"}, "Product_Code": {"type": "String", "name": "Product_Code"}, "Vendor_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Vend<PERSON>", "name": "Vendor_Name"}, "Product_Active": {"type": "Boolean", "name": "Product_Active"}, "Manufacturer": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "AltvetPet Inc.", "LexPon Inc.", "MetBeat Corp."], "name": "Manufacturer"}, "Product_Category": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Hardware", "Software", "CRM Applications"], "name": "Product_Category"}, "Sales_Start_Date": {"type": "Date", "name": "Sales_Start_Date"}, "Sales_End_Date": {"type": "Date", "name": "Sales_End_Date"}, "Support_Start_Date": {"type": "Date", "name": "Support_Start_Date"}, "Support_Expiry_Date": {"type": "Date", "name": "Support_Expiry_Date"}, "Unit_Price": {"type": "Float", "name": "Unit_Price"}, "Commission_Rate": {"type": "Float", "name": "Commission_Rate"}, "Tax": {"name": "Tax", "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Tax"}, "Usage_Unit": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "Each", "Hour(s)", "Impressions", "Lb", "M", "Pack", "Pages", "Pieces", "Quantity", "<PERSON><PERSON>", "Sheet", "Spiral Binder", "Square Feet"], "name": "Usage_Unit"}, "Qty_Ordered": {"type": "Float", "name": "Qty_Ordered"}, "Qty_in_Stock": {"type": "Float", "name": "Qty_in_Stock"}, "Reorder_Level": {"type": "Float", "name": "Reorder_Level"}, "Handler": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Handler"}, "Qty_in_Demand": {"type": "Float", "name": "Qty_in_Demand"}, "Description": {"type": "String", "name": "Description"}, "Taxable": {"type": "Boolean", "name": "Taxable"}, "Record_Image": {"type": "String", "name": "Record_Image"}, "id": {"type": "Integer", "name": "id"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}}, "quotes": {"Quote_Number": {"type": "String", "name": "Quote_Number"}, "Subject": {"required": true, "type": "String", "name": "Subject"}, "Deal_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Deals", "name": "Deal_Name"}, "Quote_Stage": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Draft", "Negotiation", "Delivered", "On Hold", "Confirmed", "Closed Won", "Closed Lost"], "name": "Quote_Stage"}, "Valid_Till": {"type": "Date", "name": "<PERSON><PERSON>_<PERSON>"}, "Team": {"type": "String", "name": "Team"}, "Contact_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Contacts", "name": "Contact_Name"}, "Carrier": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["FedEX", "UPS", "USPS", "DHL", "BlueDart"], "name": "Carrier"}, "Account_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Accounts", "skip-mandatory": true, "name": "Account_Name"}, "Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Sub_Total": {"type": "Float", "read-only": true, "name": "Sub_Total"}, "Tax": {"type": "Float", "name": "Tax"}, "Adjustment": {"type": "Float", "name": "Adjustment"}, "Grand_Total": {"type": "Float", "read-only": true, "name": "Grand_Total"}, "Billing_Street": {"type": "String", "name": "Billing_Street"}, "Shipping_Street": {"type": "String", "name": "Shipping_Street"}, "Billing_City": {"type": "String", "name": "Billing_City"}, "Shipping_City": {"type": "String", "name": "Shipping_City"}, "Billing_State": {"type": "String", "name": "Billing_State"}, "Shipping_State": {"type": "String", "name": "Shipping_State"}, "Billing_Code": {"type": "String", "name": "Billing_Code"}, "Shipping_Code": {"type": "String", "name": "Shipping_Code"}, "Billing_Country": {"type": "String", "name": "Billing_Country"}, "Shipping_Country": {"type": "String", "name": "Shipping_Country"}, "Terms_and_Conditions": {"type": "String", "name": "Terms_and_Conditions"}, "Description": {"type": "String", "name": "Description"}, "Discount": {"type": "Float", "name": "Discount"}, "Quoted_Items": {"required": true, "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Quoted_Items", "skip-mandatory": true, "subform": true, "name": "Quoted_Items"}, "id": {"type": "Integer", "name": "id"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}, "$line_tax": {"name": "$line_tax", "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.LineTax"}}, "quoted_items": {"Sequence_Number": {"type": "Integer", "name": "Sequence_Number"}, "Parent_Id": {"required": true, "type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Quotes", "name": "Parent_Id"}, "Product_Name": {"required": true, "name": "Product_Name", "type": "zohocrmsdk.src.com.zoho.crm.api.record.LineItemProduct", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.LineItemProduct", "skip-mandatory": true}, "Description": {"type": "String", "name": "Description"}, "Quantity": {"type": "Float", "name": "Quantity"}, "Price_Book_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Price_Books", "name": "Price_Book_Name"}, "List_Price": {"type": "Float", "name": "List_Price"}, "Total": {"type": "Float", "read-only": true, "name": "Total"}, "Discount": {"name": "Discount", "type": "String"}, "Total_After_Discount": {"type": "Float", "read-only": true, "name": "Total_After_Discount"}, "Tax": {"type": "Float", "name": "Tax"}, "Net_Total": {"type": "Float", "read-only": true, "name": "Net_Total"}, "Line_Tax": {"type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.LineTax", "name": "Line_Tax"}}, "price_books": {"Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Price_Book_Name": {"required": true, "type": "String", "name": "Price_Book_Name"}, "Active": {"type": "Boolean", "name": "Active"}, "Description": {"type": "String", "name": "Description"}, "Pricing_Model": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Flat", "Differential"], "name": "Pricing_Model"}, "Pricing_Details": {"name": "Pricing_Details", "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.PricingDetails", "skip-mandatory": true}, "id": {"type": "Integer", "name": "id"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}}, "sales_orders": {"SO_Number": {"type": "String", "name": "SO_Number"}, "Subject": {"required": true, "type": "String", "name": "Subject"}, "Deal_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Deals", "name": "Deal_Name"}, "Customer_No": {"type": "String", "name": "Customer_No"}, "Purchase_Order": {"type": "String", "name": "Purchase_Order"}, "Quote_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Quotes", "name": "Quote_Name"}, "Due_Date": {"type": "Date", "name": "Due_Date"}, "Pending": {"type": "String", "name": "Pending"}, "Contact_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Contacts", "name": "Contact_Name"}, "Carrier": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["FedEX", "UPS", "USPS", "DHL", "BlueDart"], "name": "Carrier"}, "Excise_Duty": {"type": "Float", "name": "Excise_Duty"}, "Sales_Commission": {"type": "Float", "name": "Sales_Commission"}, "Status": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Created", "Approved", "Delivered", "Cancelled"], "name": "Status"}, "Account_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Accounts", "skip-mandatory": true, "name": "Account_Name"}, "Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Sub_Total": {"type": "Float", "read-only": true, "name": "Sub_Total"}, "Tax": {"type": "Float", "name": "Tax"}, "Adjustment": {"type": "Float", "name": "Adjustment"}, "Grand_Total": {"type": "Float", "read-only": true, "name": "Grand_Total"}, "Billing_Street": {"type": "String", "name": "Billing_Street"}, "Shipping_Street": {"type": "String", "name": "Shipping_Street"}, "Billing_City": {"type": "String", "name": "Billing_City"}, "Shipping_City": {"type": "String", "name": "Shipping_City"}, "Billing_State": {"type": "String", "name": "Billing_State"}, "Shipping_State": {"type": "String", "name": "Shipping_State"}, "Billing_Code": {"type": "String", "name": "Billing_Code"}, "Shipping_Code": {"type": "String", "name": "Shipping_Code"}, "Billing_Country": {"type": "String", "name": "Billing_Country"}, "Shipping_Country": {"type": "String", "name": "Shipping_Country"}, "Terms_and_Conditions": {"type": "String", "name": "Terms_and_Conditions"}, "Description": {"type": "String", "name": "Description"}, "Discount": {"type": "Float", "name": "Discount"}, "Ordered_Items": {"required": true, "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Ordered_Items", "skip-mandatory": true, "subform": true, "name": "Ordered_Items"}, "id": {"type": "Integer", "name": "id"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}, "$line_tax": {"name": "$line_tax", "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.LineTax"}}, "ordered_items": {"Sequence_Number": {"type": "Integer", "name": "Sequence_Number"}, "Parent_Id": {"required": true, "type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Sales_Orders", "name": "Parent_Id"}, "Product_Name": {"required": true, "name": "Product_Name", "type": "zohocrmsdk.src.com.zoho.crm.api.record.LineItemProduct", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.LineItemProduct", "skip-mandatory": true}, "Description": {"type": "String", "name": "Description"}, "Quantity": {"type": "Float", "name": "Quantity"}, "Price_Book_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Price_Books", "name": "Price_Book_Name"}, "List_Price": {"type": "Float", "name": "List_Price"}, "Total": {"type": "Float", "read-only": true, "name": "Total"}, "Discount": {"name": "Discount", "type": "String"}, "Total_After_Discount": {"type": "Float", "read-only": true, "name": "Total_After_Discount"}, "Tax": {"type": "Float", "name": "Tax"}, "Net_Total": {"type": "Float", "read-only": true, "name": "Net_Total"}, "Line_Tax": {"type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.LineTax", "name": "Line_Tax"}}, "purchase_orders": {"PO_Number": {"type": "String", "name": "PO_Number"}, "Subject": {"required": true, "type": "String", "name": "Subject"}, "Vendor_Name": {"required": true, "type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Vend<PERSON>", "name": "Vendor_Name"}, "Requisition_No": {"type": "String", "name": "Requisition_No"}, "Tracking_Number": {"type": "String", "name": "Tracking_Number"}, "Contact_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Contacts", "name": "Contact_Name"}, "PO_Date": {"type": "Date", "name": "PO_Date"}, "Due_Date": {"type": "Date", "name": "Due_Date"}, "Carrier": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["FedEX", "UPS", "USPS", "DHL", "BlueDart"], "name": "Carrier"}, "Excise_Duty": {"type": "Float", "name": "Excise_Duty"}, "Sales_Commission": {"type": "Float", "name": "Sales_Commission"}, "Status": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Created", "Approved", "Delivered", "Cancelled"], "name": "Status"}, "Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Sub_Total": {"type": "Float", "read-only": true, "name": "Sub_Total"}, "Tax": {"type": "Float", "name": "Tax"}, "Adjustment": {"type": "Float", "name": "Adjustment"}, "Grand_Total": {"type": "Float", "read-only": true, "name": "Grand_Total"}, "Billing_Street": {"type": "String", "name": "Billing_Street"}, "Shipping_Street": {"type": "String", "name": "Shipping_Street"}, "Billing_City": {"type": "String", "name": "Billing_City"}, "Shipping_City": {"type": "String", "name": "Shipping_City"}, "Billing_State": {"type": "String", "name": "Billing_State"}, "Shipping_State": {"type": "String", "name": "Shipping_State"}, "Billing_Code": {"type": "String", "name": "Billing_Code"}, "Shipping_Code": {"type": "String", "name": "Shipping_Code"}, "Billing_Country": {"type": "String", "name": "Billing_Country"}, "Shipping_Country": {"type": "String", "name": "Shipping_Country"}, "Terms_and_Conditions": {"type": "String", "name": "Terms_and_Conditions"}, "Description": {"type": "String", "name": "Description"}, "Discount": {"type": "Float", "name": "Discount"}, "Purchase_Items": {"required": true, "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Purchase_Items", "skip-mandatory": true, "subform": true, "name": "Purchase_Items"}, "id": {"type": "Integer", "name": "id"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}, "$line_tax": {"name": "$line_tax", "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.LineTax"}}, "purchase_items": {"Sequence_Number": {"type": "Integer", "name": "Sequence_Number"}, "Parent_Id": {"required": true, "type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Purchase_Orders", "name": "Parent_Id"}, "Product_Name": {"required": true, "name": "Product_Name", "type": "zohocrmsdk.src.com.zoho.crm.api.record.LineItemProduct", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.LineItemProduct", "skip-mandatory": true}, "Description": {"type": "String", "name": "Description"}, "Quantity": {"type": "Float", "name": "Quantity"}, "Price_Book_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Price_Books", "name": "Price_Book_Name"}, "List_Price": {"type": "Float", "name": "List_Price"}, "Total": {"type": "Float", "read-only": true, "name": "Total"}, "Discount": {"name": "Discount", "type": "String"}, "Total_After_Discount": {"type": "Float", "read-only": true, "name": "Total_After_Discount"}, "Tax": {"type": "Float", "name": "Tax"}, "Net_Total": {"type": "Float", "read-only": true, "name": "Net_Total"}, "Line_Tax": {"type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.LineTax", "name": "Line_Tax"}}, "invoices": {"Invoice_Number": {"type": "String", "name": "Invoice_Number"}, "Subject": {"required": true, "type": "String", "name": "Subject"}, "Sales_Order": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Sales_Orders", "name": "Sales_Order"}, "Invoice_Date": {"type": "Date", "name": "Invoice_Date"}, "Purchase_Order": {"type": "String", "name": "Purchase_Order"}, "Due_Date": {"type": "Date", "name": "Due_Date"}, "Excise_Duty": {"type": "Float", "name": "Excise_Duty"}, "Sales_Commission": {"type": "Float", "name": "Sales_Commission"}, "Status": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Created", "Approved", "Delivered", "Cancelled"], "name": "Status"}, "Account_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Accounts", "skip-mandatory": true, "name": "Account_Name"}, "Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Sub_Total": {"type": "Float", "read-only": true, "name": "Sub_Total"}, "Tax": {"type": "Float", "name": "Tax"}, "Adjustment": {"type": "Float", "name": "Adjustment"}, "Grand_Total": {"type": "Float", "read-only": true, "name": "Grand_Total"}, "Billing_Street": {"type": "String", "name": "Billing_Street"}, "Shipping_Street": {"type": "String", "name": "Shipping_Street"}, "Billing_City": {"type": "String", "name": "Billing_City"}, "Shipping_City": {"type": "String", "name": "Shipping_City"}, "Billing_State": {"type": "String", "name": "Billing_State"}, "Shipping_State": {"type": "String", "name": "Shipping_State"}, "Billing_Code": {"type": "String", "name": "Billing_Code"}, "Shipping_Code": {"type": "String", "name": "Shipping_Code"}, "Billing_Country": {"type": "String", "name": "Billing_Country"}, "Shipping_Country": {"type": "String", "name": "Shipping_Country"}, "Terms_and_Conditions": {"type": "String", "name": "Terms_and_Conditions"}, "Description": {"type": "String", "name": "Description"}, "Contact_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Contacts", "name": "Contact_Name"}, "Discount": {"type": "Float", "name": "Discount"}, "Invoiced_Items": {"required": true, "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Invoiced_Items", "skip-mandatory": true, "subform": true, "name": "Invoiced_Items"}, "id": {"type": "Integer", "name": "id"}, "Deal_Name__s": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Deals", "name": "Deal_Name__s"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}, "$line_tax": {"name": "$line_tax", "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.LineTax"}}, "invoiced_items": {"Sequence_Number": {"type": "Integer", "name": "Sequence_Number"}, "Parent_Id": {"required": true, "type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Invoices", "name": "Parent_Id"}, "Product_Name": {"required": true, "name": "Product_Name", "type": "zohocrmsdk.src.com.zoho.crm.api.record.LineItemProduct", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.LineItemProduct", "skip-mandatory": true}, "Description": {"type": "String", "name": "Description"}, "Quantity": {"type": "Float", "name": "Quantity"}, "Price_Book_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Price_Books", "name": "Price_Book_Name"}, "List_Price": {"type": "Float", "name": "List_Price"}, "Total": {"type": "Float", "read-only": true, "name": "Total"}, "Discount": {"name": "Discount", "type": "String"}, "Total_After_Discount": {"type": "Float", "read-only": true, "name": "Total_After_Discount"}, "Tax": {"type": "Float", "name": "Tax"}, "Net_Total": {"type": "Float", "read-only": true, "name": "Net_Total"}, "Line_Tax": {"type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.LineTax", "name": "Line_Tax"}}, "cases": {"Case_Number": {"type": "String", "name": "Case_Number"}, "Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Status": {"required": true, "type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["New", "Escalated", "On Hold", "Closed"], "name": "Status"}, "Product_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Products", "name": "Product_Name"}, "Priority": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "High", "Medium", "Low"], "name": "Priority"}, "Type": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Problem", "Feature Request", "Question"], "name": "Type"}, "Case_Reason": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "User did not attend any training", "Complex functionality", "Existing problem", "Instructions not clear", "New problem"], "name": "Case_Reason"}, "Case_Origin": {"required": true, "type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Email", "Phone", "Web"], "name": "Case_Origin"}, "Subject": {"required": true, "type": "String", "name": "Subject"}, "Related_To": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Contacts", "name": "Related_To"}, "No_of_comments": {"type": "Integer", "name": "No_of_comments"}, "Reported_By": {"type": "String", "name": "Reported_By"}, "Email": {"type": "String", "name": "Email"}, "Phone": {"type": "String", "name": "Phone"}, "Description": {"type": "String", "name": "Description"}, "Internal_Comments": {"type": "String", "name": "Internal_Comments"}, "Solution": {"type": "String", "name": "Solution"}, "Add_Comment": {"type": "String", "name": "Add_Comment"}, "Comments": {"name": "Comments", "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Comment", "lookup": true}, "Account_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Accounts", "skip-mandatory": true, "name": "Account_Name"}, "Deal_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Deals", "name": "Deal_Name"}, "id": {"type": "Integer", "name": "id"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}}, "solutions": {"Solution_Number": {"type": "String", "name": "Solution_Number"}, "Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Solution_Title": {"required": true, "type": "String", "name": "Solution_Title"}, "Published": {"type": "Boolean", "name": "Published"}, "Status": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Draft", "Reviewed", "Duplicate"], "name": "Status"}, "Product_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Products", "name": "Product_Name"}, "No_of_comments": {"type": "Integer", "name": "No_of_comments"}, "Question": {"type": "String", "name": "Question"}, "Answer": {"type": "String", "name": "Answer"}, "Add_Comment": {"type": "String", "name": "Add_Comment"}, "Comments": {"name": "Comments", "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Comment", "lookup": true}, "id": {"type": "Integer", "name": "id"}, "Locked__s": {"type": "Boolean", "name": "Locked__s"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}}, "visits": {}, "dealhistory": {"Potential_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Deals", "name": "Potential_Name"}, "Stage": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Qualification", "Needs Analysis", "Value Proposition", "Identify Decision Makers", "Proposal/Price Quote", "Negotiation/Review", "Closed Won", "Closed Lost", "Closed Lost to Competition"], "name": "Stage"}, "Stage_Duration_Calendar_Days": {"type": "Integer", "name": "Stage_Duration_Calendar_Days"}, "Amount": {"type": "Float", "name": "Amount"}, "Probability": {"type": "Integer", "name": "Probability"}, "Expected_Revenue": {"type": "Float", "name": "Expected_Revenue"}, "Closing_Date": {"type": "Date", "name": "Closing_Date"}, "id": {"type": "Integer", "name": "id"}, "Last_Activity_Time": {"type": "DateTime", "name": "Last_Activity_Time"}, "Moved_To__s": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Qualification", "Needs Analysis", "Value Proposition", "Identify Decision Makers", "Proposal/Price Quote", "Negotiation/Review", "Closed Won", "Closed Lost", "Closed Lost to Competition"], "name": "Moved_To__s"}}, "email_sentiment": {"Positive": {"type": "Integer", "name": "Positive"}, "Negative": {"type": "Integer", "name": "Negative"}, "Neutral": {"type": "Integer", "name": "Neutral"}, "Received_Date": {"type": "DateTime", "name": "Received_Date"}, "Seid": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "name": "<PERSON><PERSON>"}, "User": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "User"}, "Module": {"type": "zohocrmsdk.src.com.zoho.crm.api.modules.Modules", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.modules.Modules", "name": "<PERSON><PERSON><PERSON>"}, "Email": {"type": "String", "name": "Email"}}, "email_analytics": {"User": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "User"}, "Mail_Source": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Individual", "Work<PERSON>low Alert", "Mass Email"], "name": "Mail_Source"}, "Entity_Source": {"type": "zohocrmsdk.src.com.zoho.crm.api.modules.Modules", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.modules.Modules", "name": "Entity_Source"}, "Date": {"type": "DateTime", "name": "Date"}, "Sent": {"type": "Integer", "name": "<PERSON><PERSON>"}, "Bounced": {"type": "Integer", "name": "Bounced"}, "Tracked": {"type": "Integer", "name": "Tracked"}, "Bounced_Among_Tracked": {"type": "Integer", "name": "Bounced_Among_Tracked"}, "Opened": {"type": "Integer", "name": "Opened"}, "Clicked": {"type": "Integer", "name": "Clicked"}, "Replied": {"type": "Integer", "name": "Replied"}, "Received": {"type": "Integer", "name": "Received"}, "Responded": {"type": "Integer", "name": "Responded"}, "Amount": {"type": "Float", "name": "Amount"}, "Dialled_Attended": {"type": "Integer", "name": "Dialled_Attended"}, "Dialled_UnAttended": {"type": "Integer", "name": "Dialled_UnAttended"}, "Call_Received": {"type": "Integer", "name": "Call_Received"}, "Call_Missed": {"type": "Integer", "name": "Call_Missed"}, "Total_Call_Duration": {"type": "Integer", "name": "Total_Call_Duration"}, "Events_Created": {"type": "Integer", "name": "Events_Created"}, "Event_Duration": {"type": "Integer", "name": "Event_Duration"}, "Checkins": {"type": "Integer", "name": "Checkins"}, "Task_Completed": {"type": "Integer", "name": "Task_Completed"}, "id": {"type": "Integer", "name": "id"}}, "email_template_analytics": {"User": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "User"}, "Template_Name": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "module": "Email_Template__s", "name": "Template_Name"}, "Mail_Source": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["-None-", "Individual", "Work<PERSON>low Alert", "Mass Email"], "name": "Mail_Source"}, "Entity_Source": {"type": "zohocrmsdk.src.com.zoho.crm.api.modules.Modules", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.modules.Modules", "name": "Entity_Source"}, "Date": {"type": "DateTime", "name": "Date"}, "Sent": {"type": "Integer", "name": "<PERSON><PERSON>"}, "Bounced": {"type": "Integer", "name": "Bounced"}, "Tracked": {"type": "Integer", "name": "Tracked"}, "Bounced_Among_Tracked": {"type": "Integer", "name": "Bounced_Among_Tracked"}, "Opened": {"type": "Integer", "name": "Opened"}, "Clicked": {"type": "Integer", "name": "Clicked"}, "Replied": {"type": "Integer", "name": "Replied"}, "Received": {"type": "Integer", "name": "Received"}, "id": {"type": "Integer", "name": "id"}}, "email_template__s": {"id": {"type": "Integer", "name": "id"}, "name": {"type": "String", "name": "name"}}, "notes": {"Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "Note_Title": {"type": "String", "name": "Note_Title"}, "Note_Content": {"type": "String", "name": "Note_Content"}, "Parent_Id": {"required": true, "type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "name": "Parent_Id"}, "id": {"type": "Integer", "name": "id"}, "$attachments": {"name": "$attachments", "type": "List", "structure_name": "zohocrmsdk.src.com.zoho.crm.api.attachments.Attachment"}}, "attachments": {"Owner": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Owner"}, "File_Name": {"required": true, "type": "String", "name": "File_Name"}, "Size": {"required": true, "type": "Integer", "name": "Size"}, "Parent_Id": {"required": true, "type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "name": "Parent_Id"}, "id": {"type": "Integer", "name": "id"}}, "actions_performed": {}, "locking_information__s": {"Locked_Reason__s": {"type": "String", "name": "Locked_Reason__s"}, "Lock_Source__s": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["Manual", "Automatic"], "name": "Lock_Source__s"}, "Locked_For__s": {"type": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "lookup": true, "skip-mandatory": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.record.Record", "name": "Locked_For__s"}, "Locked_Time__s": {"type": "DateTime", "name": "Locked_Time__s"}, "Locked_By__s": {"type": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "lookup": true, "structure_name": "zohocrmsdk.src.com.zoho.crm.api.users.MinifiedUser", "name": "Locked_By__s"}, "id": {"type": "Integer", "name": "id"}, "Record_Locking_Rule_Id__s": {"type": "Integer", "name": "Record_Locking_Rule_Id__s"}, "Record_Locking_Configuration_Id__s": {"type": "Integer", "name": "Record_Locking_Configuration_Id__s"}, "Feature_Type__s": {"type": "zohocrmsdk.src.com.zoho.crm.api.util.Choice", "picklist": true, "values": ["record_locking", "orchestration", "find_and_merge"], "name": "Feature_Type__s"}}}