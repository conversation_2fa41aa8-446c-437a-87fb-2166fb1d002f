# OAuth Setup Guide for Zoho CRM MCP Server

This guide helps you set up OAuth authentication for your Zoho CRM server-based application.

## Problem

When you create a **server-based application** in Zoho CRM, you only receive:

- `client_id`
- `client_secret`

However, the Zoho CRM Python SDK requires additional tokens:

- `refresh_token` (for automatic token refresh)
- `access_token` (generated automatically from refresh token)

## Solution

Use the provided OAuth helper tools to complete the OAuth 2.0 flow and obtain the required tokens.

## Quick Setup (Recommended)

### Method 1: Using the Callback Server (Easiest)

1. **Start the OAuth callback server**:

   ```bash
   python oauth_callback_server.py --auto-open
   ```

   This will:

   - Start a local server on http://localhost:8000
   - Automatically open the authorization URL in your browser

2. **Grant permissions in your browser**:

   - Log in to your Zoho account
   - Review and accept the requested permissions
   - You'll be redirected to a success page with your authorization code

3. **Copy the authorization code** from the success page

4. **Stop the callback server** (Ctrl+C)

5. **Exchange the code for tokens**:

   ```bash
   python oauth_helper.py --exchange-code YOUR_CODE_HERE
   ```

6. **Update your .env file** with the refresh token shown

### Method 2: Manual Process

If the automatic method doesn't work, use the manual process below:

## Step-by-Step Setup (Manual)

### Step 1: Generate Authorization URL

```bash
python oauth_helper.py --get-auth-url
```

This will output:

- Your environment (US/EU/IN)
- Your client ID
- **Authorization URL** to visit

### Step 2: Grant Permissions

1. **Copy the authorization URL** from Step 1
2. **Open it in your browser**
3. **Log in to your Zoho account**
4. **Grant the requested permissions**:
   - ZohoCRM.modules.ALL
   - ZohoCRM.settings.ALL
   - ZohoCRM.users.ALL
5. **Copy the authorization code** from the redirect URL

The redirect URL will look like:

```
http://localhost:8000/callback?code=1000.abc123def456...&location=us&accounts-server=https%3A%2F%2Faccounts.zoho.com
```

Copy the value after `code=` (everything before the next `&`).

### Step 3: Exchange Code for Tokens

```bash
python oauth_helper.py --exchange-code YOUR_CODE_HERE
```

Replace `YOUR_CODE_HERE` with the code from Step 2.

This will:

- Exchange the code for access and refresh tokens
- Test the refresh token
- Show you the refresh token to add to your `.env` file

### Step 4: Update Environment File

Add the refresh token to your `.env` file:

```env
ZOHO_REFRESH_TOKEN=1000.your_refresh_token_here
```

### Step 5: Test Your Setup

```bash
python oauth_helper.py --test-token YOUR_REFRESH_TOKEN
```

Or simply start your MCP server:

```bash
python src/main.py
```

## Troubleshooting

### Invalid Client Error

- Verify your `client_id` and `client_secret` in `.env`
- Ensure your Zoho application is configured as "Server-based Application"

### Redirect URI Mismatch

- The default redirect URI is `http://localhost:8000/callback`
- If you configured a different redirect URI in Zoho, use:
  ```bash
  python oauth_helper.py --get-auth-url --redirect-uri YOUR_REDIRECT_URI
  python oauth_helper.py --exchange-code YOUR_CODE --redirect-uri YOUR_REDIRECT_URI
  ```

### Token Expired

- Refresh tokens don't expire, but if you get authentication errors:
- Re-run the OAuth flow from Step 1

### Environment Issues

- Ensure your `ZOHO_ENVIRONMENT` in `.env` matches your Zoho account region:
  - `US` for zoho.com accounts
  - `EU` for zoho.eu accounts
  - `IN` for zoho.in accounts

## Security Notes

- **Never commit your refresh token** to version control
- The refresh token provides long-term access to your Zoho CRM
- Store it securely and rotate it periodically
- The SDK automatically handles access token refresh

## Technical Details

This implementation follows the [Zoho CRM Python SDK v8.0 documentation](https://github.com/zoho/zohocrm-python-sdk-8.0) patterns:

- Uses `OAuthToken` with refresh token flow
- Implements `FileStore` for token persistence
- Follows proper SDK initialization patterns
- Handles automatic token refresh

The OAuth helper script uses Context7-validated patterns for maximum compatibility.
