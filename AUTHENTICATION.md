# Zoho CRM MCP Server Authentication

This document describes the OAuth 2.1 authentication system implemented for the Zoho CRM MCP server.

## Overview

The authentication system provides:

- **OAuth 2.1 compliance** with PKCE (Proof Key for Code Exchange)
- **Stateless token validation** using <PERSON>oh<PERSON>'s introspection endpoint
- **Structured error responses** with clear authentication requirements
- **Bearer token authentication** for all API requests
- **Automatic token validation caching** for performance

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MCP Client    │    │  Auth Middleware │    │   MCP Server    │
│                 │    │                  │    │                 │
│ Bearer Token ──→│───→│ Token Validator ──│───→│ Protected APIs  │
│                 │    │                  │    │                 │
│                 │    │ ↓ (if invalid)   │    │                 │
│ ←── 401 Error ──│←───│ Structured Error │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ↓
                       ┌─────────────────┐
                       │ Zoho OAuth API  │
                       │ (Introspection) │
                       └─────────────────┘
```

## Getting Started

### 1. Enable Authentication

Set `AUTH_ENABLED=true` in your environment variables or `.env` file:

```bash
# Authentication Configuration
AUTH_ENABLED=true
ZOHO_CLIENT_ID=your_client_id
ZOHO_CLIENT_SECRET=your_client_secret
ZOHO_ENVIRONMENT=US  # US, EU, or IN
```

### 2. Start the Server

```bash
python src/main.py
```

The server will start with authentication enabled and provide endpoints at:

- `http://localhost:8000/auth/*` - Authentication endpoints
- `http://localhost:8000/*` - Protected MCP APIs

## Authentication Flow

### Step 1: Get Authorization URL

Make a request to get the OAuth authorization URL:

```bash
curl http://localhost:8000/auth/authorize
```

Response:

```json
{
  "authorization_url": "https://accounts.zoho.com/oauth/v2/auth?...",
  "state": "random_state_value",
  "code_verifier": "store_this_securely",
  "instructions": {
    "step_1": "Visit the authorization_url in your browser",
    "step_2": "Grant the requested permissions",
    "step_3": "You will be redirected back with an authorization code",
    "step_4": "Use the authorization code to exchange for access tokens"
  }
}
```

### Step 2: Visit Authorization URL

Open the `authorization_url` in your browser and grant the requested permissions. You'll be redirected back with an authorization code.

### Step 3: Exchange Code for Token

Use Zoho's token endpoint to exchange the authorization code for an access token:

```bash
curl -X POST https://accounts.zoho.com/oauth/v2/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=authorization_code" \
  -d "client_id=your_client_id" \
  -d "client_secret=your_client_secret" \
  -d "redirect_uri=http://localhost:8000/auth/callback" \
  -d "code=authorization_code_from_step_2" \
  -d "code_verifier=code_verifier_from_step_1"
```

### Step 4: Use Access Token

Include the access token in the Authorization header for all API requests:

```bash
curl -H "Authorization: Bearer your_access_token" \
  http://localhost:8000/api/your-endpoint
```

## API Endpoints

### Authentication Endpoints

| Endpoint              | Method | Description                           |
| --------------------- | ------ | ------------------------------------- |
| `/auth/authorize`     | GET    | Get OAuth authorization URL with PKCE |
| `/auth/callback`      | GET    | OAuth callback handler                |
| `/auth/validate`      | POST   | Validate an access token              |
| `/auth/requirements`  | GET    | Get authentication requirements       |
| `/auth/provider-info` | GET    | Get OAuth provider information        |
| `/auth/health`        | GET    | Authentication system health check    |

### Protected Endpoints

All MCP API endpoints require authentication:

| Endpoint                          | Method | Description        |
| --------------------------------- | ------ | ------------------ |
| `/create_record`                  | POST   | Create CRM records |
| `/fetch_records`                  | POST   | Fetch CRM records  |
| `/search_records`                 | POST   | Search CRM records |
| `/zoho://modules/{module}/schema` | GET    | Get module schema  |
| `/zoho://modules/{module}/fields` | GET    | Get module fields  |

## Error Responses

When authentication fails, the server returns structured error responses:

### 401 Unauthorized - Missing Token

```json
{
  "error": {
    "code": "AUTHENTICATION_REQUIRED",
    "message": "Authentication required to access this resource",
    "details": {
      "authentication_requirements": [
        {
          "provider": "zoho",
          "auth_type": "bearer",
          "header_name": "Authorization",
          "header_format": "Bearer {access_token}",
          "required_scopes": [
            "ZohoCRM.modules.ALL",
            "ZohoCRM.settings.ALL",
            "ZohoCRM.users.ALL",
            "ZohoCRM.org.ALL"
          ],
          "token_source": "access_token",
          "authorization_url": "/auth/authorize",
          "oauth_endpoints": {
            "authorization": "https://accounts.zoho.com/oauth/v2/auth",
            "token": "https://accounts.zoho.com/oauth/v2/token",
            "introspection": "https://accounts.zoho.com/oauth/v2/token/info"
          }
        }
      ]
    }
  }
}
```

### 401 Unauthorized - Invalid Token

```json
{
  "error": {
    "code": "INVALID_TOKEN",
    "message": "Invalid or malformed access token",
    "details": { ... }
  }
}
```

### 401 Unauthorized - Expired Token

```json
{
  "error": {
    "code": "TOKEN_EXPIRED",
    "message": "Access token has expired. Please re-authenticate.",
    "details": { ... }
  }
}
```

### 403 Forbidden - Insufficient Scopes

```json
{
  "error": {
    "code": "INSUFFICIENT_SCOPES",
    "message": "Token missing required scopes. Required: ZohoCRM.modules.ALL, ZohoCRM.settings.ALL",
    "details": { ... }
  }
}
```

## Configuration

### Environment Variables

| Variable             | Default                               | Description                          |
| -------------------- | ------------------------------------- | ------------------------------------ |
| `AUTH_ENABLED`       | `true`                                | Enable/disable authentication        |
| `AUTH_CACHE_TTL`     | `300`                                 | Token validation cache TTL (seconds) |
| `ZOHO_CLIENT_ID`     | Required                              | Zoho OAuth client ID                 |
| `ZOHO_CLIENT_SECRET` | Required                              | Zoho OAuth client secret             |
| `ZOHO_ENVIRONMENT`   | `US`                                  | Zoho environment (US, EU, IN)        |
| `OAUTH_REDIRECT_URI` | `http://localhost:8000/auth/callback` | OAuth redirect URI                   |

### Required Scopes

The server requires these OAuth scopes:

- `ZohoCRM.modules.ALL` - Access to all CRM modules
- `ZohoCRM.settings.ALL` - Access to CRM settings
- `ZohoCRM.users.ALL` - Access to user information
- `ZohoCRM.org.ALL` - Access to organization information

### Excluded Paths

These paths don't require authentication:

- `/auth/*` - Authentication endpoints
- `/health` - Health check
- `/docs` - API documentation
- `/openapi.json` - OpenAPI specification

## Security Features

### PKCE (Proof Key for Code Exchange)

The system uses PKCE to protect against authorization code interception attacks:

- Code verifier: Random 43-128 character string
- Code challenge: SHA256 hash of the code verifier
- Challenge method: S256

### Token Validation

- **Real-time validation**: Tokens are validated with Zoho on each request
- **Caching**: Validation results are cached for 5 minutes by default
- **Introspection**: Uses RFC 7662 token introspection standard
- **Scope checking**: Validates required scopes for each endpoint

### State Parameter

CSRF protection using cryptographically secure random state parameters.

## Development

### Running Tests

```bash
# Install test dependencies
pip install -r requirements.txt

# Run authentication tests
pytest tests/test_auth.py -v

# Run all tests
pytest tests/ -v
```

### Debugging

Enable debug logging:

```bash
export LOG_LEVEL=DEBUG
python src/main.py
```

### Disable Authentication

For development, you can disable authentication:

```bash
export AUTH_ENABLED=false
python src/main.py
```

## Troubleshooting

### Common Issues

1. **Invalid Client ID/Secret**

   - Verify your Zoho OAuth app credentials
   - Check the environment (US/EU/IN) matches your Zoho account

2. **Token Validation Fails**

   - Ensure token is not expired
   - Verify token has required scopes
   - Check network connectivity to Zoho

3. **CORS Issues**
   - Configure proper redirect URIs in Zoho OAuth app
   - Ensure redirect URI matches exactly

### Error Codes

| Code                      | Description             | Solution                                     |
| ------------------------- | ----------------------- | -------------------------------------------- |
| `AUTHENTICATION_REQUIRED` | No token provided       | Include Bearer token in Authorization header |
| `INVALID_TOKEN`           | Token is malformed      | Get a new token via OAuth flow               |
| `TOKEN_EXPIRED`           | Token has expired       | Refresh token or re-authenticate             |
| `INSUFFICIENT_SCOPES`     | Missing required scopes | Re-authorize with all required scopes        |
| `OAUTH_PROVIDER_ERROR`    | Zoho API error          | Check Zoho service status                    |

## Migration from Legacy Auth

If upgrading from the legacy environment variable-based authentication:

1. Set `AUTH_ENABLED=true`
2. Remove `ZOHO_REFRESH_TOKEN` from environment
3. Use OAuth flow to get access tokens
4. Update client applications to use Bearer tokens

The server maintains backward compatibility when `AUTH_ENABLED=false`.
