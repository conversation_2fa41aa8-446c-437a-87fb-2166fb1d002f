#!/usr/bin/env python3
"""
Test script to verify OAuth flow consistency
"""
import asyncio
import json
from src.auth.zoho_provider import ZohoOAuthProvider
from src.config import Settings


async def test_oauth_flow():
    """Test the complete OAuth flow"""

    # Load settings
    settings = Settings()

    # Initialize OAuth provider
    oauth_provider = ZohoOAuthProvider(
        client_id=settings.zoho_client_id,
        client_secret=settings.zoho_client_secret,
        environment=settings.zoho_environment,
        redirect_uri=settings.oauth_redirect_uri,
    )

    print("🔧 OAuth Configuration:")
    print(f"   Client ID: {settings.zoho_client_id}")
    print(f"   Environment: {settings.zoho_environment}")
    print(f"   Redirect URI: {settings.oauth_redirect_uri}")
    print()

    # Step 1: Create authorization request
    print("📝 Step 1: Creating authorization request...")
    auth_request = oauth_provider.create_authorization_request()

    print("✅ Authorization request created:")
    print(f"   State: {auth_request['state']}")
    print(f"   Code Verifier: {auth_request['code_verifier'][:20]}...")
    print(f"   Code Challenge: {auth_request['code_challenge'][:20]}...")
    print()

    print("🌐 Authorization URL:")
    print(auth_request["authorization_url"])
    print()

    print("📋 Instructions:")
    print("1. Copy the authorization URL above")
    print("2. Open it in your browser")
    print("3. Grant permissions")
    print("4. Copy the 'code' parameter from the callback URL")
    print("5. Use this information for token exchange:")
    print()

    print("🔑 Token Exchange Parameters:")
    print(f"   authorization_code: [CODE_FROM_CALLBACK]")
    print(f"   code_verifier: {auth_request['code_verifier']}")
    print(f"   redirect_uri: {settings.oauth_redirect_uri}")
    print()

    print("📡 MCP Tool Call Example:")
    example_call = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": "exchange_code_for_token",
            "arguments": {
                "authorization_code": "[CODE_FROM_CALLBACK]",
                "code_verifier": auth_request["code_verifier"],
                "redirect_uri": settings.oauth_redirect_uri,
            },
        },
    }
    print(json.dumps(example_call, indent=2))


if __name__ == "__main__":
    asyncio.run(test_oauth_flow())
