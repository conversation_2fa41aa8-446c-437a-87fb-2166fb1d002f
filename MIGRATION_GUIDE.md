# Migration Guide: From Complex SDK to Simple REST API

This guide helps you migrate from the complex SDK-based implementation to the clean, simple REST API version.

## 🎯 What Changed

### Before (Complex)

- **870+ lines** of SDK wrapper code
- **12+ dependencies** including heavy `zohocrmsdk8_0`
- **Complex authentication** middleware and OAuth flows
- **Dynamic SDK initialization** with Bearer token extraction
- **Multiple auth files** and complex error handling

### After (Simple)

- **254 lines** of clean REST API code
- **5 dependencies** - only essential packages
- **Direct HTTP calls** using `httpx`
- **Simple token authentication** via parameters or environment
- **Single file** OAuth helper

## 🚀 Migration Steps

### 1. Backup Current Setup

```bash
# Backup your current working directory
cp -r . ../zoho-mcp-backup
```

### 2. Install New Dependencies

```bash
# Uninstall old heavy dependencies
pip uninstall zohocrmsdk8_0 -y

# Install new minimal dependencies
pip install -r requirements_simple.txt
```

### 3. Update Environment Variables

Your `.env` file structure remains the same:

```env
# Keep these (same as before)
ZOHO_CLIENT_ID=your_client_id
ZOHO_CLIENT_SECRET=your_client_secret
ZOHO_ENVIRONMENT=US

# These will be added by simple_oauth.py
ZOHO_ACCESS_TOKEN=your_access_token
ZOHO_REFRESH_TOKEN=your_refresh_token
```

### 4. Get New OAuth Tokens

```bash
# Run the simple OAuth flow
python simple_oauth.py
```

### 5. Switch to Simple Server

```bash
# Old way
python src/main.py

# New way
python src/simple_main.py
```

## 🔄 API Changes

### Tool Calls - Before vs After

#### Create Record

**Before (Complex):**

```json
{
  "tool": "create_record",
  "arguments": {
    "module": "Leads",
    "record_data": { "Last_Name": "Smith" },
    "duplicate_check": true,
    "trigger_workflows": false
  },
  "headers": {
    "Authorization": "Bearer your_token"
  }
}
```

**After (Simple):**

```json
{
  "tool": "create_record",
  "arguments": {
    "module": "Leads",
    "record_data": { "Last_Name": "Smith" },
    "access_token": "your_token"
  }
}
```

#### Get Records

**Before (Complex):**

```json
{
  "tool": "fetch_records",
  "arguments": {
    "module": "Leads",
    "fields": ["Last_Name", "Company"],
    "criteria": { "Lead_Status": "Open" },
    "page": 1,
    "per_page": 50
  },
  "headers": {
    "Authorization": "Bearer your_token"
  }
}
```

**After (Simple):**

```json
{
  "tool": "get_records",
  "arguments": {
    "module": "Leads",
    "fields": ["Last_Name", "Company"],
    "page": 1,
    "per_page": 50,
    "access_token": "your_token"
  }
}
```

#### Search Records

**Before (Complex):**

```json
{
  "tool": "search_records",
  "arguments": {
    "modules": ["Leads"],
    "search_text": "<EMAIL>",
    "max_results": 100
  },
  "headers": {
    "Authorization": "Bearer your_token"
  }
}
```

**After (Simple):**

```json
{
  "tool": "search_records",
  "arguments": {
    "module": "Leads",
    "search_criteria": "(Email:equals:<EMAIL>)",
    "access_token": "your_token"
  }
}
```

## 🔧 Key Differences

### Authentication

| Aspect             | Before                  | After                         |
| ------------------ | ----------------------- | ----------------------------- |
| **Method**         | Bearer token in headers | Token as parameter or env var |
| **Complexity**     | Middleware extraction   | Direct parameter              |
| **Initialization** | Dynamic SDK init        | No initialization needed      |

### Error Handling

| Aspect              | Before                         | After                     |
| ------------------- | ------------------------------ | ------------------------- |
| **SDK Errors**      | Complex SDK exception handling | Simple HTTP status codes  |
| **Auth Errors**     | Multiple error types           | Clear HTTP error messages |
| **Response Format** | SDK response wrappers          | Direct JSON responses     |

### Performance

| Aspect            | Before                    | After                      |
| ----------------- | ------------------------- | -------------------------- |
| **Startup Time**  | Slow (SDK initialization) | Fast (no SDK)              |
| **Memory Usage**  | High (SDK overhead)       | Low (minimal dependencies) |
| **Request Speed** | Slower (SDK abstraction)  | Faster (direct HTTP)       |

## 🧪 Testing Migration

### 1. Test OAuth Flow

```bash
python simple_oauth.py
```

### 2. Test API Calls

```bash
python test_simple.py
```

### 3. Test MCP Server

```bash
python src/simple_main.py
```

## 🚨 Breaking Changes

### Removed Features

- ❌ **Complex middleware**: No more authentication middleware
- ❌ **Dynamic initialization**: No SDK initialization complexity
- ❌ **Bearer header extraction**: Use direct token parameters
- ❌ **Multi-module search**: Search one module at a time
- ❌ **Advanced filtering**: Use Zoho's criteria syntax directly

### Changed Tool Names

- `fetch_records` → `get_records`
- `search_records` (multi-module) → `search_records` (single module)

### Changed Parameters

- **Authentication**: From headers to parameters
- **Search**: From `search_text` to `search_criteria` with Zoho syntax
- **Filtering**: From complex criteria objects to Zoho criteria strings

## 🎉 Benefits After Migration

### Development Experience

- ✅ **Faster development**: No SDK complexity to understand
- ✅ **Easier debugging**: Direct HTTP calls are transparent
- ✅ **Better documentation**: REST API docs are clearer than SDK docs
- ✅ **Simpler testing**: Easy to test with curl or Postman

### Performance

- ✅ **80% less code**: 254 lines vs 870+ lines
- ✅ **60% fewer dependencies**: 5 packages vs 12+ packages
- ✅ **Faster startup**: No SDK initialization overhead
- ✅ **Lower memory usage**: No SDK objects in memory

### Maintenance

- ✅ **Easier updates**: No SDK version compatibility issues
- ✅ **Better error messages**: Direct API error responses
- ✅ **Simpler deployment**: Fewer dependencies to manage
- ✅ **Clear code flow**: Direct HTTP calls are self-documenting

## 🆘 Troubleshooting

### Common Issues

**1. "Access token required" error**

```bash
# Solution: Set environment variable or pass as parameter
export ZOHO_ACCESS_TOKEN=your_token
# OR pass access_token parameter to tools
```

**2. "HTTP_401" authentication error**

```bash
# Solution: Refresh your access token
python simple_oauth.py --refresh
```

**3. "Invalid criteria" search error**

```bash
# Solution: Use proper Zoho criteria syntax
# Before: {"Email": "<EMAIL>"}
# After: "(Email:equals:<EMAIL>)"
```

## 📞 Support

If you encounter issues during migration:

1. **Check the test script**: `python test_simple.py`
2. **Verify OAuth tokens**: `python simple_oauth.py --refresh`
3. **Compare with examples**: See `README_SIMPLE.md`
4. **Check API documentation**: Zoho CRM REST API v6 docs

The simplified version is designed to be more reliable and easier to troubleshoot than the complex SDK-based version.
