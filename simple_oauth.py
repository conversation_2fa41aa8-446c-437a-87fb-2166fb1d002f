#!/usr/bin/env python3
"""
Simple OAuth helper for Zoho CRM - No SDK required
Clean implementation using direct REST API calls
"""

import requests
import urllib.parse
import webbrowser
import sys
from typing import Dict, Optional


class SimpleZohoOAuth:
    """Simple Zoho OAuth implementation using REST APIs"""

    def __init__(self, client_id: str, client_secret: str, environment: str = "US"):
        self.client_id = client_id
        self.client_secret = client_secret
        self.environment = environment

        # OAuth endpoints by environment
        self.oauth_domains = {
            "US": "https://accounts.zoho.com",
            "EU": "https://accounts.zoho.eu",
            "IN": "https://accounts.zoho.in",
            "AU": "https://accounts.zoho.com.au",
            "CN": "https://accounts.zoho.com.cn",
        }
        self.oauth_base = self.oauth_domains.get(environment, self.oauth_domains["US"])

    def get_authorization_url(
        self,
        redirect_uri: str = "http://localhost:8080/callback",
        scopes: Optional[list] = None,
    ) -> str:
        """Generate authorization URL"""
        if scopes is None:
            scopes = [
                "ZohoCRM.modules.ALL",
                "ZohoCRM.settings.ALL",
                "ZohoCRM.users.ALL",
                "ZohoCRM.org.ALL",
            ]

        params = {
            "response_type": "code",
            "client_id": self.client_id,
            "scope": ",".join(scopes),
            "redirect_uri": redirect_uri,
            "access_type": "offline",
        }

        auth_url = f"{self.oauth_base}/oauth/v2/auth?" + urllib.parse.urlencode(params)
        return auth_url

    def exchange_code_for_tokens(
        self,
        authorization_code: str,
        redirect_uri: str = "http://localhost:8080/callback",
    ) -> Dict:
        """Exchange authorization code for access and refresh tokens"""
        token_url = f"{self.oauth_base}/oauth/v2/token"

        data = {
            "grant_type": "authorization_code",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "redirect_uri": redirect_uri,
            "code": authorization_code,
        }

        response = requests.post(token_url, data=data)

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(
                f"Token exchange failed: {response.status_code} - {response.text}"
            )

    def refresh_access_token(self, refresh_token: str) -> Dict:
        """Refresh access token using refresh token"""
        token_url = f"{self.oauth_base}/oauth/v2/token"

        data = {
            "grant_type": "refresh_token",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": refresh_token,
        }

        response = requests.post(token_url, data=data)

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(
                f"Token refresh failed: {response.status_code} - {response.text}"
            )


def main():
    """Interactive OAuth flow"""
    import os
    from dotenv import load_dotenv

    load_dotenv()

    client_id = os.getenv("ZOHO_CLIENT_ID")
    client_secret = os.getenv("ZOHO_CLIENT_SECRET")
    environment = os.getenv("ZOHO_ENVIRONMENT", "US")

    if not client_id or not client_secret:
        print(
            "❌ Error: ZOHO_CLIENT_ID and ZOHO_CLIENT_SECRET must be set in .env file"
        )
        sys.exit(1)

    oauth = SimpleZohoOAuth(client_id, client_secret, environment)

    if len(sys.argv) > 1 and sys.argv[1] == "--refresh":
        # Refresh token flow
        refresh_token = input("Enter refresh token: ").strip()
        try:
            result = oauth.refresh_access_token(refresh_token)
            print("\n✅ Token refreshed successfully!")
            print(f"Access Token: {result['access_token']}")
            print(f"Expires in: {result.get('expires_in', 'Unknown')} seconds")

            # Save to .env
            with open(".env", "a") as f:
                f.write(f"\nZOHO_ACCESS_TOKEN={result['access_token']}\n")
            print("💾 Access token saved to .env file")

        except Exception as e:
            print(f"❌ Error refreshing token: {e}")
            sys.exit(1)

    else:
        # Authorization code flow
        print("🚀 Starting Zoho OAuth Flow")
        print(f"Environment: {environment}")

        # Step 1: Get authorization URL
        auth_url = oauth.get_authorization_url()
        print(f"\n📋 Step 1: Visit this URL to authorize the application:")
        print(f"🔗 {auth_url}")

        # Try to open browser automatically
        try:
            webbrowser.open(auth_url)
            print("🌐 Browser opened automatically")
        except:
            print("⚠️  Could not open browser automatically")

        # Step 2: Get authorization code
        print(
            f"\n📋 Step 2: After granting permissions, copy the 'code' parameter from the callback URL"
        )
        auth_code = input("Enter authorization code: ").strip()

        if not auth_code:
            print("❌ Authorization code is required")
            sys.exit(1)

        # Step 3: Exchange code for tokens
        try:
            print("\n🔄 Exchanging code for tokens...")
            result = oauth.exchange_code_for_tokens(auth_code)

            print("\n✅ OAuth flow completed successfully!")
            print(f"Access Token: {result['access_token']}")
            print(f"Refresh Token: {result['refresh_token']}")
            print(f"Expires in: {result.get('expires_in', 'Unknown')} seconds")

            # Save tokens to .env
            with open(".env", "a") as f:
                f.write(f"\nZOHO_ACCESS_TOKEN={result['access_token']}\n")
                f.write(f"ZOHO_REFRESH_TOKEN={result['refresh_token']}\n")

            print("\n💾 Tokens saved to .env file")
            print("🎉 You can now use the MCP server!")

        except Exception as e:
            print(f"❌ Error exchanging code for tokens: {e}")
            sys.exit(1)


if __name__ == "__main__":
    main()
