# OAuth Scope Mismatch Troubleshooting Guide

## Problem: OAUTH_SCOPE_MISMATCH Error

If you're seeing this error when running your Zoho CRM MCP Server:

```
SDKException: Caused By: API_EXCEPTION - {"code": "OAUTH_SCOPE_MISMATCH", "status": "error", "message": "..."}
```

This means your OAuth token doesn't have the required permissions (scopes) for the operations being attempted.

## Quick Diagnosis

Run this command to diagnose your current token's scope issues:

```bash
python oauth_helper.py --diagnose-scopes YOUR_CURRENT_REFRESH_TOKEN
```

This will test access to different Zoho CRM APIs and tell you exactly which scopes are missing.

## Root Causes

### 1. Insufficient Scopes in Zoho Console

Your Zoho application might not be configured with all required scopes.

**Required Scopes:**

- `ZohoCRM.modules.ALL` - Access to all CRM modules (Leads, Contacts, etc.)
- `ZohoCRM.settings.ALL` - Access to CRM settings and metadata
- `ZohoCRM.users.ALL` - Access to user information
- `ZohoCRM.org.ALL` - Access to organization details
- `ZohoCRM.bulk.ALL` - Access to bulk operations
- `ZohoCRM.notifications.ALL` - Access to notifications

### 2. Outdated Refresh Token

Your refresh token might have been generated with fewer scopes than currently required.

### 3. Incorrect Application Type

Ensure your Zoho application is configured as a **Server-based Application**, not a web application.

## Solution Steps

### Step 1: Verify Zoho Console Configuration

1. Go to [Zoho API Console](https://api-console.zoho.com/)
2. Select your application
3. Go to **Client Secret** tab
4. Verify these scopes are enabled:
   - ZohoCRM.modules.ALL
   - ZohoCRM.settings.ALL
   - ZohoCRM.users.ALL
   - ZohoCRM.org.ALL
   - ZohoCRM.bulk.ALL
   - ZohoCRM.notifications.ALL

### Step 2: Generate New OAuth Token

If scopes were missing or you're unsure, generate a new token:

```bash
# 1. Generate authorization URL with updated scopes
python oauth_helper.py --get-auth-url

# 2. Visit the URL in your browser
# 3. Grant ALL requested permissions (don't skip any)
# 4. Copy the authorization code from the redirect URL

# 5. Exchange code for tokens
python oauth_helper.py --exchange-code YOUR_AUTHORIZATION_CODE

# 6. Update your .env file with the new refresh token
```

### Step 3: Test the New Token

```bash
python oauth_helper.py --test-token YOUR_NEW_REFRESH_TOKEN
```

### Step 4: Verify Scope Access

```bash
python oauth_helper.py --diagnose-scopes YOUR_NEW_REFRESH_TOKEN
```

This should show all API access tests passing.

## Common Issues

### "Invalid Client" Error

- Double-check your `ZOHO_CLIENT_ID` and `ZOHO_CLIENT_SECRET` in `.env`
- Ensure your application is published (not in draft mode)

### "Redirect URI Mismatch"

- Verify your redirect URI in Zoho Console matches `http://localhost:8000/callback`
- Or use custom redirect URI: `python oauth_helper.py --get-auth-url --redirect-uri YOUR_URI`

### "Access Denied" During Authorization

- Make sure you're logged into the correct Zoho account
- Grant ALL requested permissions (don't uncheck any scopes)

### Environment Mismatch

Ensure your `ZOHO_ENVIRONMENT` matches your account:

- `US` for zoho.com accounts
- `EU` for zoho.eu accounts
- `IN` for zoho.in accounts

## Advanced Troubleshooting

### Check Token Details

You can inspect your current token's scopes by making a direct API call:

```bash
curl -X GET \
  "https://accounts.zoho.com/oauth/user/info" \
  -H "Authorization: Zoho-oauthtoken YOUR_ACCESS_TOKEN"
```

### Manual Scope Testing

Test individual API endpoints to isolate scope issues:

```python
# Test modules access
from zohocrmsdk.src.com.zoho.crm.api.modules import ModulesOperations
modules_ops = ModulesOperations()
response = modules_ops.get_modules()
print(f"Modules API: {response.get_status_code()}")

# Test users access
from zohocrmsdk.src.com.zoho.crm.api.users import UsersOperations
users_ops = UsersOperations()
response = users_ops.get_users()
print(f"Users API: {response.get_status_code()}")
```

## Prevention

1. **Always use comprehensive scopes** when creating OAuth applications
2. **Test tokens immediately** after generation
3. **Document your scopes** for future reference
4. **Set up monitoring** to detect scope issues early

## Still Having Issues?

If you continue to experience scope mismatch errors:

1. Delete your current application in Zoho Console
2. Create a new Server-based Application
3. Configure ALL required scopes from the start
4. Generate fresh OAuth tokens
5. Test thoroughly before deploying

The OAuth scope system is strict - it's better to request more permissions than needed rather than too few.
