#!/usr/bin/env python3
"""
OAuth Callback Server for Zoho CRM MCP Server

This script runs a simple HTTP server to handle the OAuth callback redirect.
It captures the authorization code and displays it for easy copying.

Usage:
1. python oauth_callback_server.py
2. In another terminal: python oauth_helper.py --get-auth-url
3. Visit the authorization URL in your browser
4. Grant permissions - you'll be redirected to this server
5. Copy the authorization code displayed
6. Stop this server (Ctrl+C)
7. python oauth_helper.py --exchange-code YOUR_CODE
"""

import http.server
import socketserver
import urllib.parse
import webbrowser
import threading
import time
from src.config import Settings


class OAuthCallbackHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path.startswith("/callback"):
            # Parse the query parameters
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)

            # Extract the authorization code
            auth_code = query_params.get("code", [None])[0]
            error = query_params.get("error", [None])[0]

            if error:
                self.send_response(400)
                self.send_header("Content-type", "text/html")
                self.end_headers()

                html_content = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>OAuth Error - Zoho CRM MCP Server</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }}
                        .container {{ max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                        .error {{ color: #d32f2f; background-color: #ffebee; padding: 15px; border-radius: 4px; margin: 20px 0; }}
                        .code {{ background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; word-break: break-all; }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>❌ OAuth Authorization Failed</h1>
                        <div class="error">
                            <strong>Error:</strong> {error}
                        </div>
                        <p>The OAuth authorization was not successful. Please try again.</p>
                        <p>You can close this window and restart the OAuth process.</p>
                    </div>
                </body>
                </html>
                """

                self.wfile.write(html_content.encode())
                print(f"\n❌ OAuth Error: {error}")

            elif auth_code:
                self.send_response(200)
                self.send_header("Content-type", "text/html")
                self.end_headers()

                html_content = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>OAuth Success - Zoho CRM MCP Server</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }}
                        .container {{ max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                        .success {{ color: #2e7d32; background-color: #e8f5e8; padding: 15px; border-radius: 4px; margin: 20px 0; }}
                        .code {{ background-color: #f5f5f5; padding: 15px; border-radius: 4px; font-family: monospace; word-break: break-all; font-size: 14px; }}
                        .copy-btn {{ background-color: #1976d2; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 10px; }}
                        .copy-btn:hover {{ background-color: #1565c0; }}
                        .command {{ background-color: #263238; color: #4fc3f7; padding: 15px; border-radius: 4px; font-family: monospace; margin: 10px 0; }}
                    </style>
                    <script>
                        function copyCode() {{
                            navigator.clipboard.writeText('{auth_code}').then(function() {{
                                document.getElementById('copy-btn').textContent = '✅ Copied!';
                                setTimeout(function() {{
                                    document.getElementById('copy-btn').textContent = '📋 Copy Code';
                                }}, 2000);
                            }});
                        }}
                    </script>
                </head>
                <body>
                    <div class="container">
                        <h1>✅ OAuth Authorization Successful!</h1>
                        <div class="success">
                            <strong>Success!</strong> You have successfully authorized the Zoho CRM MCP Server.
                        </div>
                        
                        <h3>📋 Your Authorization Code:</h3>
                        <div class="code" id="auth-code">{auth_code}</div>
                        <button class="copy-btn" id="copy-btn" onclick="copyCode()">📋 Copy Code</button>
                        
                        <h3>🚀 Next Steps:</h3>
                        <p>1. Copy the authorization code above</p>
                        <p>2. Stop the callback server (Ctrl+C in the terminal)</p>
                        <p>3. Run this command in your terminal:</p>
                        
                        <div class="command">python oauth_helper.py --exchange-code {auth_code}</div>
                        
                        <p>4. Update your .env file with the refresh token</p>
                        <p>5. Start your MCP server: <code>python src/main.py</code></p>
                        
                        <p><em>You can close this window after copying the code.</em></p>
                    </div>
                </body>
                </html>
                """

                self.wfile.write(html_content.encode())
                print(f"\n✅ Authorization Code Received!")
                print(f"📋 Code: {auth_code}")
                print(f"\n🚀 Next step: Run this command:")
                print(f"   python oauth_helper.py --exchange-code {auth_code}")

            else:
                self.send_response(400)
                self.send_header("Content-type", "text/html")
                self.end_headers()

                html_content = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>OAuth Callback - Zoho CRM MCP Server</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
                        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>❓ OAuth Callback</h1>
                        <p>This is the OAuth callback endpoint for the Zoho CRM MCP Server.</p>
                        <p>No authorization code was received. Please restart the OAuth process.</p>
                    </div>
                </body>
                </html>
                """

                self.wfile.write(html_content.encode())
        else:
            # Handle other paths
            self.send_response(404)
            self.send_header("Content-type", "text/html")
            self.end_headers()

            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Zoho CRM MCP Server - OAuth Callback</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
                    .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🔗 Zoho CRM MCP Server</h1>
                    <p>OAuth Callback Server is running...</p>
                    <p>Waiting for OAuth authorization callback at <code>/callback</code></p>
                    <p>Use the OAuth helper to generate an authorization URL.</p>
                </div>
            </body>
            </html>
            """

            self.wfile.write(html_content.encode())

    def log_message(self, format, *args):
        # Suppress default HTTP server logs
        pass


def run_callback_server(port=8000):
    """Run the OAuth callback server"""
    try:
        with socketserver.TCPServer(("", port), OAuthCallbackHandler) as httpd:
            print(f"🌐 OAuth Callback Server running on http://localhost:{port}")
            print(f"📍 Callback URL: http://localhost:{port}/callback")
            print(f"⏳ Waiting for OAuth authorization...")
            print(f"💡 In another terminal, run: python oauth_helper.py --get-auth-url")
            print(f"🛑 Press Ctrl+C to stop the server")
            print()

            httpd.serve_forever()

    except KeyboardInterrupt:
        print(f"\n🛑 Callback server stopped.")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {port} is already in use.")
            print(
                f"💡 Try a different port: python oauth_callback_server.py --port 8001"
            )
        else:
            print(f"❌ Error starting server: {e}")


def main():
    import argparse

    parser = argparse.ArgumentParser(description="OAuth Callback Server for Zoho CRM")
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to run the callback server on (default: 8000)",
    )
    parser.add_argument(
        "--auto-open",
        action="store_true",
        help="Automatically open the authorization URL in browser",
    )

    args = parser.parse_args()

    if args.auto_open:
        # Start server in background and open auth URL
        def start_server():
            time.sleep(1)  # Give server time to start
            try:
                settings = Settings()
                from oauth_helper import ZohoOAuthHelper

                helper = ZohoOAuthHelper()
                auth_url = helper.get_authorization_url(
                    f"http://localhost:{args.port}/callback"
                )
                print(f"🔗 Opening authorization URL in browser...")
                webbrowser.open(auth_url)
            except Exception as e:
                print(f"❌ Could not open browser: {e}")
                print(f"💡 Manually run: python oauth_helper.py --get-auth-url")

        # Start server in background thread
        server_thread = threading.Thread(target=start_server)
        server_thread.daemon = True
        server_thread.start()

    run_callback_server(args.port)


if __name__ == "__main__":
    main()
