#!/usr/bin/env python3
"""
Test script for the modular Zoho CRM MCP Server
Tests basic functionality and tool availability.
"""

import asyncio
import json
import httpx
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

SERVER_URL = "http://127.0.0.1:8000"
TEST_TOKEN = "test_bearer_token_for_testing"


async def test_server():
    """Test the modular MCP server"""

    async with httpx.AsyncClient() as client:

        # Test 1: Health Check (no auth required for this test)
        logger.info("🔍 Testing health_check tool...")
        try:
            health_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {"name": "health_check", "arguments": {}},
            }

            response = await client.post(
                SERVER_URL,
                json=health_request,
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json, text/event-stream",
                    "Authorization": f"Bearer {TEST_TOKEN}",
                },
            )

            if response.status_code == 200:
                result = response.json()
                logger.info("✅ Health check successful")
                logger.info(f"📊 Response: {json.dumps(result, indent=2)}")
            else:
                logger.error(f"❌ Health check failed: {response.status_code}")
                logger.error(f"Response: {response.text}")

        except Exception as e:
            logger.error(f"❌ Health check error: {e}")

        # Test 2: List Tools
        logger.info("\n🔍 Testing tools/list...")
        try:
            list_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list",
                "params": {},
            }

            response = await client.post(
                SERVER_URL,
                json=list_request,
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json, text/event-stream",
                    "Authorization": f"Bearer {TEST_TOKEN}",
                },
            )

            if response.status_code == 200:
                result = response.json()
                logger.info("✅ Tools list successful")
                tools = result.get("result", {}).get("tools", [])
                logger.info(f"📊 Available tools: {[tool['name'] for tool in tools]}")
            else:
                logger.error(f"❌ Tools list failed: {response.status_code}")
                logger.error(f"Response: {response.text}")

        except Exception as e:
            logger.error(f"❌ Tools list error: {e}")

        # Test 3: Get Tool Info
        logger.info("\n🔍 Testing get_tool_info tool...")
        try:
            tool_info_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": "get_tool_info",
                    "arguments": {"tool_name": "create_record"},
                },
            }

            response = await client.post(
                SERVER_URL,
                json=tool_info_request,
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json, text/event-stream",
                    "Authorization": f"Bearer {TEST_TOKEN}",
                },
            )

            if response.status_code == 200:
                result = response.json()
                logger.info("✅ Tool info successful")
                logger.info(f"📊 Tool info available")
            else:
                logger.error(f"❌ Tool info failed: {response.status_code}")
                logger.error(f"Response: {response.text}")

        except Exception as e:
            logger.error(f"❌ Tool info error: {e}")

        # Test 4: Test authentication error handling
        logger.info("\n🔍 Testing authentication error handling...")
        try:
            auth_test_request = {
                "jsonrpc": "2.0",
                "id": 4,
                "method": "tools/call",
                "params": {"name": "get_records", "arguments": {"module": "Leads"}},
            }

            # Test without proper auth header
            response = await client.post(
                SERVER_URL,
                json=auth_test_request,
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json, text/event-stream",
                    # No Authorization header
                },
            )

            if response.status_code == 200:
                result = response.json()
                logger.info("✅ Authentication error handling working")
                logger.info("📊 Expected authentication error received")
            else:
                logger.error(f"❌ Authentication test failed: {response.status_code}")

        except Exception as e:
            logger.error(f"❌ Authentication test error: {e}")


if __name__ == "__main__":
    logger.info("🚀 Starting modular server tests...")
    logger.info(f"🌐 Testing server at: {SERVER_URL}")
    logger.info("⚠️  Make sure the server is running with: python -m src.main_new")

    asyncio.run(test_server())

    logger.info("\n✅ Test completed!")
    logger.info("💡 If all tests passed, the modular server is working correctly!")
