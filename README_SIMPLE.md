# Simple Zoho CRM MCP Server

A clean, lightweight MCP server for Zoho CRM using direct REST API calls instead of heavy SDK dependencies.

## ✨ Features

- **🪶 Lightweight**: No heavy SDK dependencies (254 lines vs 870+ lines)
- **🚀 Fast**: Direct REST API calls using `httpx`
- **🔧 Simple**: Easy to understand and modify
- **🌍 Multi-region**: Supports US, EU, IN, AU, CN data centers
- **🔐 OAuth 2.0**: Simple OAuth implementation without SDK bloat

## 📦 Installation

```bash
# Install minimal dependencies
pip install -r requirements_simple.txt
```

## 🔧 Setup

1. **Create `.env` file:**

```env
ZOHO_CLIENT_ID=your_client_id
ZOHO_CLIENT_SECRET=your_client_secret
ZOHO_ENVIRONMENT=US
```

2. **Get OAuth tokens:**

```bash
# Run interactive OAuth flow
python simple_oauth.py

# Or refresh existing token
python simple_oauth.py --refresh
```

3. **Run the MCP server:**

```bash
python src/simple_main.py
```

## 🛠️ Available Tools

### `create_record`

Create a new record in Zoho CRM.

```python
{
    "module": "Leads",
    "record_data": {
        "Last_Name": "Smith",
        "Company": "ABC Corp",
        "Email": "<EMAIL>"
    },
    "access_token": "your_access_token"
}
```

### `get_records`

Retrieve records from a module.

```python
{
    "module": "Leads",
    "fields": ["Last_Name", "Company", "Email"],
    "page": 1,
    "per_page": 50,
    "access_token": "your_access_token"
}
```

### `search_records`

Search records using criteria.

```python
{
    "module": "Leads",
    "search_criteria": "(Email:equals:<EMAIL>)",
    "access_token": "your_access_token"
}
```

### `health_check`

Check server health and configuration.

## 🌍 Supported Environments

- **US**: `https://www.zohoapis.com/crm/v6`
- **EU**: `https://www.zohoapis.eu/crm/v6`
- **IN**: `https://www.zohoapis.in/crm/v6`
- **AU**: `https://www.zohoapis.com.au/crm/v6`
- **CN**: `https://www.zohoapis.com.cn/crm/v6`

## 🔐 Authentication

The server supports two ways to provide access tokens:

1. **Parameter**: Pass `access_token` parameter to each tool
2. **Environment**: Set `ZOHO_ACCESS_TOKEN` environment variable

## 📊 Comparison with Original

| Feature             | Original     | Simple     |
| ------------------- | ------------ | ---------- |
| **Lines of Code**   | 870+         | 254        |
| **Dependencies**    | 12+ packages | 5 packages |
| **SDK Dependency**  | ✅ Heavy     | ❌ None    |
| **Auth Complexity** | ✅ Complex   | ❌ Simple  |
| **Startup Time**    | ✅ Slow      | ❌ Fast    |
| **Memory Usage**    | ✅ High      | ❌ Low     |
| **Maintainability** | ✅ Complex   | ❌ Simple  |

## 🚀 Quick Start

```bash
# 1. Clone and setup
git clone <repo>
cd zoho-mcp
pip install -r requirements_simple.txt

# 2. Configure
cp .env.example .env
# Edit .env with your Zoho app credentials

# 3. Get tokens
python simple_oauth.py

# 4. Run server
python src/simple_main.py
```

## 🔧 Development

The simplified version removes:

- ❌ Heavy Zoho SDK (`zohocrmsdk8_0`)
- ❌ Complex authentication middleware
- ❌ Multiple auth files
- ❌ Dynamic SDK initialization
- ❌ Complex error handling layers
- ❌ Unnecessary dependencies

And keeps:

- ✅ Core MCP functionality
- ✅ Direct REST API calls
- ✅ Simple OAuth flow
- ✅ Clean error handling
- ✅ Multi-environment support

## 📝 License

Same as original project.
